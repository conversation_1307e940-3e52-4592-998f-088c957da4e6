<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= teacher.name %> - Curriculum Vitae</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.3;
            color: #000;
            margin: 0;
            padding: 10px;
            background: white;
        }

        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 15mm;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #000;
        }

        .header h1 {
            color: #000;
            margin: 0;
            font-size: 20px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header h2 {
            color: #000;
            margin: 5px 0;
            font-size: 16px;
            font-weight: normal;
        }

        .header .department {
            color: #000;
            font-size: 14px;
            margin-top: 3px;
        }
        
        .section {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }

        .section-title {
            background: #000;
            color: white;
            padding: 8px 12px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 10px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid #ccc;
        }

        .contact-label {
            font-weight: bold;
            color: #000;
            min-width: 100px;
            margin-right: 10px;
        }

        .contact-value {
            color: #000;
            flex: 1;
        }
        
        .experience-item, .education-item {
            background: white;
            padding: 8px;
            margin-bottom: 6px;
            border: 1px solid #000;
            border-left: 3px solid #000;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .item-title {
            font-weight: bold;
            color: #000;
            font-size: 14px;
        }

        .item-date {
            color: #000;
            font-size: 12px;
            background: #f0f0f0;
            padding: 2px 6px;
            border: 1px solid #000;
            white-space: nowrap;
        }

        .item-subtitle {
            color: #000;
            font-style: italic;
            margin-bottom: 3px;
            font-size: 13px;
        }

        .item-description {
            color: #000;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 8px;
        }

        .skill-category {
            background: white;
            padding: 8px;
            border: 1px solid #000;
            border-left: 3px solid #000;
        }

        .skill-category-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 4px;
            font-size: 14px;
            text-transform: capitalize;
        }

        .skill-list {
            color: #000;
            font-size: 13px;
            line-height: 1.3;
        }

        .achievement-item {
            background: white;
            padding: 8px;
            margin-bottom: 6px;
            border: 1px solid #000;
            border-left: 3px solid #000;
        }

        .achievement-title {
            font-weight: bold;
            color: #000;
            margin-bottom: 3px;
            font-size: 14px;
        }

        .achievement-description {
            color: #000;
            font-size: 13px;
            line-height: 1.3;
        }
        
        .timeline-container {
            position: relative;
            padding-left: 15px;
        }

        .timeline-container::before {
            content: '';
            position: absolute;
            left: 6px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #000;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 12px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 6px;
            width: 8px;
            height: 8px;
            background: #000;
            border-radius: 50%;
            border: 2px solid white;
        }

        .summary-text {
            background: white;
            padding: 10px;
            border: 1px solid #000;
            border-left: 3px solid #000;
            color: #000;
            line-height: 1.4;
            font-size: 14px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            color: #000;
            font-size: 12px;
        }

        .page-break {
            page-break-after: always;
        }

        @media print {
            body { margin: 0; padding: 0; }
            .cv-container { padding: 10mm; }
            .page-break { page-break-after: always; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header Section -->
        <div class="header">
            <h1><%= teacher.name || 'Teacher Name' %></h1>
            <h2><%= teacher.designation || 'Teacher' %></h2>
            <div class="department"><%= teacher.department || 'Academic Department' %></div>
        </div>

        <!-- Professional Summary -->
        <div class="section">
            <div class="section-title">Professional Summary</div>
            <div class="summary-text">
                <%
                let summary = '';
                if (teacher.total_experience_years) {
                    summary += `Experienced educator with ${teacher.total_experience_years} years of professional experience`;
                    if (teacher.teaching_experience_years) {
                        summary += `, including ${teacher.teaching_experience_years} years in teaching`;
                    }
                    summary += '. ';
                }
                
                if (teacher.subjects_taught) {
                    summary += `Specialized in ${teacher.subjects_taught}. `;
                }
                
                summary += 'Committed to educational excellence and student development with a proven track record of academic achievement and professional growth.';
                %>
                <%= summary %>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="section">
            <div class="section-title">Contact Information</div>
            <div class="contact-grid">
                <% if (teacher.email) { %>
                <div class="contact-item">
                    <div class="contact-label">Email:</div>
                    <div class="contact-value"><%= teacher.email %></div>
                </div>
                <% } %>
                
                <% if (teacher.phone) { %>
                <div class="contact-item">
                    <div class="contact-label">Phone:</div>
                    <div class="contact-value"><%= teacher.phone %></div>
                </div>
                <% } %>
                
                <% if (teacher.employee_id) { %>
                <div class="contact-item">
                    <div class="contact-label">Employee ID:</div>
                    <div class="contact-value"><%= teacher.employee_id %></div>
                </div>
                <% } %>
                
                <% if (teacher.date_of_birth) { %>
                <div class="contact-item">
                    <div class="contact-label">Date of Birth:</div>
                    <div class="contact-value"><%= new Date(teacher.date_of_birth).toLocaleDateString() %></div>
                </div>
                <% } %>
                
                <% if (teacher.gender) { %>
                <div class="contact-item">
                    <div class="contact-label">Gender:</div>
                    <div class="contact-value"><%= teacher.gender %></div>
                </div>
                <% } %>
                
                <% if (teacher.joining_date) { %>
                <div class="contact-item">
                    <div class="contact-label">Joining Date:</div>
                    <div class="contact-value"><%= new Date(teacher.joining_date).toLocaleDateString() %></div>
                </div>
                <% } %>
            </div>
        </div>

        <!-- Professional Experience -->
        <% if (teacher.experienceTimeline && teacher.experienceTimeline.length > 0) { %>
        <div class="section">
            <div class="section-title">Professional Experience</div>
            <div class="timeline-container">
                <% teacher.experienceTimeline.forEach(exp => { %>
                <div class="timeline-item">
                    <div class="experience-item">
                        <div class="item-header">
                            <div class="item-title"><%= exp.title || exp.position || 'Position' %></div>
                            <div class="item-date"><%= exp.duration || exp.year || 'Duration' %></div>
                        </div>
                        <div class="item-subtitle"><%= exp.institution || exp.organization || 'Organization' %></div>
                        <% if (exp.description) { %>
                        <div class="item-description"><%= exp.description %></div>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Education -->
        <% if (teacher.educationTimeline && teacher.educationTimeline.length > 0) { %>
        <div class="section">
            <div class="section-title">Educational Qualifications</div>
            <div class="timeline-container">
                <% teacher.educationTimeline.forEach(edu => { %>
                <div class="timeline-item">
                    <div class="education-item">
                        <div class="item-header">
                            <div class="item-title"><%= edu.title || edu.degree || 'Degree' %></div>
                            <div class="item-date"><%= edu.year || 'Year' %></div>
                        </div>
                        <div class="item-subtitle"><%= edu.institution || edu.university || 'Institution' %></div>
                        <% if (edu.percentage) { %>
                        <div class="item-description">Performance: <%= edu.percentage %>%</div>
                        <% } %>
                        <% if (edu.specialization) { %>
                        <div class="item-description">Specialization: <%= edu.specialization %></div>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } %>

        <!-- Skills & Competencies -->
        <% if (teacher.skillsByCategory && Object.keys(teacher.skillsByCategory).length > 0) { %>
        <div class="section">
            <div class="section-title">Skills & Competencies</div>
            <div class="skills-grid">
                <% Object.entries(teacher.skillsByCategory).forEach(([category, skills]) => { %>
                <div class="skill-category">
                    <div class="skill-category-title"><%= category.replace('_', ' ') %></div>
                    <div class="skill-list">
                        <% if (Array.isArray(skills)) { %>
                            <% skills.forEach((skill, index) => { %>
                                <% if (typeof skill === 'object' && skill.name) { %>
                                    <%= skill.name %><% if (skill.proficiency) { %> (<%= skill.proficiency %>)<% } %><% if (index < skills.length - 1) { %>, <% } %>
                                <% } else if (typeof skill === 'string') { %>
                                    <%= skill %><% if (index < skills.length - 1) { %>, <% } %>
                                <% } %>
                            <% }); %>
                        <% } else if (typeof skills === 'object') { %>
                            <% Object.values(skills).forEach((skill, index, arr) => { %>
                                <% if (typeof skill === 'object' && skill.name) { %>
                                    <%= skill.name %><% if (skill.proficiency) { %> (<%= skill.proficiency %>)<% } %><% if (index < arr.length - 1) { %>, <% } %>
                                <% } else if (typeof skill === 'string') { %>
                                    <%= skill %><% if (index < arr.length - 1) { %>, <% } %>
                                <% } %>
                            <% }); %>
                        <% } else { %>
                            <%= skills %>
                        <% } %>
                    </div>
                </div>
                <% }); %>
            </div>
        </div>
        <% } else if (teacher.special_skills || teacher.languages_known) { %>
        <div class="section">
            <div class="section-title">Skills & Competencies</div>
            <div class="skills-grid">
                <% if (teacher.special_skills) { %>
                <div class="skill-category">
                    <div class="skill-category-title">Special Skills</div>
                    <div class="skill-list"><%= teacher.special_skills %></div>
                </div>
                <% } %>
                <% if (teacher.languages_known) { %>
                <div class="skill-category">
                    <div class="skill-category-title">Languages Known</div>
                    <div class="skill-list"><%= teacher.languages_known %></div>
                </div>
                <% } %>
            </div>
        </div>
        <% } %>

        <!-- Achievements & Recognition -->
        <% if (teacher.achievementsByCategory && Object.keys(teacher.achievementsByCategory).length > 0) { %>
        <div class="section">
            <div class="section-title">Achievements & Recognition</div>
            <% Object.entries(teacher.achievementsByCategory).forEach(([category, achievements]) => { %>
                <% if (Array.isArray(achievements)) { %>
                    <% achievements.forEach(achievement => { %>
                    <div class="achievement-item">
                        <div class="achievement-title"><%= achievement.title || achievement.name || 'Achievement' %></div>
                        <% if (achievement.description) { %>
                        <div class="achievement-description"><%= achievement.description %></div>
                        <% } %>
                        <% if (achievement.date) { %>
                        <div class="achievement-description">Date: <%= new Date(achievement.date).toLocaleDateString() %></div>
                        <% } %>
                    </div>
                    <% }); %>
                <% } %>
            <% }); %>
        </div>
        <% } else if (teacher.awards_received) { %>
        <div class="section">
            <div class="section-title">Achievements & Recognition</div>
            <div class="achievement-item">
                <div class="achievement-title">Awards & Recognition</div>
                <div class="achievement-description"><%= teacher.awards_received %></div>
            </div>
        </div>
        <% } %>

        <!-- Publications & Research -->
        <% if (teacher.publications || teacher.research_papers || teacher.conferences_attended) { %>
        <div class="section">
            <div class="section-title">Publications & Research</div>

            <% if (teacher.publications) { %>
            <div class="experience-item">
                <div class="item-title">Publications</div>
                <div class="item-description"><%= teacher.publications %></div>
            </div>
            <% } %>

            <% if (teacher.research_papers) { %>
            <div class="experience-item">
                <div class="item-title">Research Papers</div>
                <div class="item-description"><%= teacher.research_papers %></div>
            </div>
            <% } %>

            <% if (teacher.conferences_attended) { %>
            <div class="experience-item">
                <div class="item-title">Conferences Attended</div>
                <div class="item-description"><%= teacher.conferences_attended %></div>
            </div>
            <% } %>
        </div>
        <% } %>

        <!-- Professional Certifications -->
        <% if (teacher.certifications && teacher.certifications.length > 0) { %>
        <div class="section">
            <div class="section-title">Professional Certifications</div>
            <% teacher.certifications.forEach(cert => { %>
            <div class="experience-item">
                <div class="item-header">
                    <div class="item-title"><%= cert.name || cert.certification_name || 'Certification' %></div>
                    <% if (cert.issueDate || cert.issue_date) { %>
                    <div class="item-date"><%= new Date(cert.issueDate || cert.issue_date).getFullYear() %></div>
                    <% } %>
                </div>
                <% if (cert.issuer || cert.issuing_organization) { %>
                <div class="item-subtitle"><%= cert.issuer || cert.issuing_organization %></div>
                <% } %>
                <% if (cert.description) { %>
                <div class="item-description"><%= cert.description %></div>
                <% } %>
                <% if (cert.certificateId || cert.certificate_id) { %>
                <div class="item-description">Certificate ID: <%= cert.certificateId || cert.certificate_id %></div>
                <% } %>
            </div>
            <% }); %>
        </div>
        <% } else if (teacher.professional_certifications) { %>
        <div class="section">
            <div class="section-title">Professional Certifications</div>
            <div class="experience-item">
                <div class="item-description"><%= teacher.professional_certifications %></div>
            </div>
        </div>
        <% } %>

        <!-- Training Programs -->
        <% if (teacher.training_programs) { %>
        <div class="section">
            <div class="section-title">Training Programs</div>
            <div class="experience-item">
                <div class="item-description"><%= teacher.training_programs %></div>
            </div>
        </div>
        <% } %>

        <!-- Additional Information -->
        <% if (teacher.bio || teacher.notes || teacher.combinedNotes) { %>
        <div class="section">
            <div class="section-title">Additional Information</div>
            <div class="summary-text">
                <%= teacher.combinedNotes || teacher.notes || teacher.bio %>
            </div>
        </div>
        <% } %>

        <!-- Administrative Details -->
        <div class="section">
            <div class="section-title">Administrative Information</div>
            <div class="contact-grid">
                <% if (teacher.employment_type) { %>
                <div class="contact-item">
                    <div class="contact-label">Employment Type:</div>
                    <div class="contact-value"><%= teacher.employment_type %></div>
                </div>
                <% } %>

                <% if (teacher.confirmation_date) { %>
                <div class="contact-item">
                    <div class="contact-label">Confirmation Date:</div>
                    <div class="contact-value"><%= new Date(teacher.confirmation_date).toLocaleDateString() %></div>
                </div>
                <% } %>

                <% if (teacher.performance_rating) { %>
                <div class="contact-item">
                    <div class="contact-label">Performance Rating:</div>
                    <div class="contact-value"><%= teacher.performance_rating %></div>
                </div>
                <% } %>

                <% if (teacher.office_location) { %>
                <div class="contact-item">
                    <div class="contact-label">Office Location:</div>
                    <div class="contact-value"><%= teacher.office_location %></div>
                </div>
                <% } %>

                <% if (teacher.current_salary) { %>
                <div class="contact-item">
                    <div class="contact-label">Current Salary:</div>
                    <div class="contact-value">₹<%= teacher.current_salary %></div>
                </div>
                <% } %>

                <% if (teacher.account_status) { %>
                <div class="contact-item">
                    <div class="contact-label">Account Status:</div>
                    <div class="contact-value"><%= teacher.account_status %></div>
                </div>
                <% } %>

                <% if (teacher.total_experience_years) { %>
                <div class="contact-item">
                    <div class="contact-label">Total Experience:</div>
                    <div class="contact-value"><%= teacher.total_experience_years %> years</div>
                </div>
                <% } %>

                <% if (teacher.teaching_experience_years) { %>
                <div class="contact-item">
                    <div class="contact-label">Teaching Experience:</div>
                    <div class="contact-value"><%= teacher.teaching_experience_years %> years</div>
                </div>
                <% } %>

                <% if (teacher.subjects_taught) { %>
                <div class="contact-item">
                    <div class="contact-label">Subjects Taught:</div>
                    <div class="contact-value"><%= teacher.subjects_taught %></div>
                </div>
                <% } %>

                <% if (teacher.classes_handled) { %>
                <div class="contact-item">
                    <div class="contact-label">Classes Handled:</div>
                    <div class="contact-value"><%= teacher.classes_handled %></div>
                </div>
                <% } %>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Generated on:</strong> <%= new Date().toLocaleDateString() %> at <%= new Date().toLocaleTimeString() %></p>
            <p>Teacher Management System - Curriculum Vitae</p>
            <p>This document contains confidential information and is intended for official use only.</p>
        </div>
    </div>
</body>
</html>
