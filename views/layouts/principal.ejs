<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Principal Dashboard</title>

    <!-- Tailwind CSS - Production Build -->
    <link rel="stylesheet" href="/styles.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Principal Custom CSS -->
    <link rel="stylesheet" href="/css/principal.css">

    <!-- Custom CSS -->
    <style>
        .sidebar-link {
            transition: all 0.3s ease;
        }
        .sidebar-link:hover {
            background-color: rgba(30, 58, 138, 0.1);
            border-left: 4px solid #f59e0b;
        }
        .sidebar-link.active {
            background-color: rgba(30, 58, 138, 0.15);
            border-left: 4px solid #1e3a8a;
            color: #1e3a8a;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-excellent { background-color: #10b981; }
        .status-good { background-color: #3b82f6; }
        .status-average { background-color: #f59e0b; }
        .status-poor { background-color: #dc2626; }

        .progress-bar {
            transition: width 0.3s ease;
        }

        .card-hover {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.15);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        }

        .executive-card {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #f59e0b 100%);
            border: 2px solid #d97706;
        }

        .leadership-badge {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: #0f172a;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="leadership-badge px-3 py-1 rounded-full mr-3">
                            <i class="fas fa-university text-lg mr-2"></i>
                            <span class="text-sm font-bold">EXECUTIVE</span>
                        </div>
                        <span class="text-xl font-bold">Principal Command Center</span>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Real-time clock -->
                    <div class="text-sm">
                        <span id="current-time"></span>
                    </div>

                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 rounded-full hover:bg-principal-secondary transition-colors">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-yellow-500 text-xs rounded-full h-5 w-5 flex items-center justify-center">5</span>
                        </button>
                    </div>

                    <!-- User info and logout -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm">
                            <i class="fas fa-user-circle text-lg"></i>
                            <span class="font-medium">Principal</span>
                        </div>
                        <a href="/logout" class="flex items-center space-x-2 px-3 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors">
                            <i class="fas fa-sign-out-alt text-sm"></i>
                            <span class="text-sm font-medium">Logout</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-lg min-h-screen">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="/principal/dashboard" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'dashboard' ? 'active' : '' %>">
                        <i class="fas fa-satellite-dish mr-3 text-principal-primary"></i>
                        Command Center
                    </a>

                    <a href="/principal/academic-progress" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'academic-progress' ? 'active' : '' %>">
                        <i class="fas fa-chart-line mr-3 text-principal-secondary"></i>
                        Academic Intelligence
                    </a>

                    <a href="/principal/teacher-management" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'teacher-management' ? 'active' : '' %>">
                        <i class="fas fa-user-tie mr-3 text-principal-accent"></i>
                        Faculty Details
                    </a>

                    <a href="/principal/teacher-timetables" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'teacher-timetables' ? 'active' : '' %>">
                        <i class="fas fa-calendar-check mr-3 text-principal-gold"></i>
                        Strategic Calendar
                    </a>

                    <a href="/principal/student-analytics" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'student-analytics' ? 'active' : '' %>">
                        <i class="fas fa-users-cog mr-3 text-blue-600"></i>
                        Student Intelligence
                    </a>

                    <a href="/principal/students" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'students' ? 'active' : '' %>">
                        <i class="fas fa-user-graduate mr-3 text-indigo-600"></i>
                        Student Data Overview
                    </a>

                    <a href="/principal/infrastructure" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'infrastructure' ? 'active' : '' %>">
                        <i class="fas fa-building mr-3 text-green-600"></i>
                        Infrastructure Command
                    </a>

                    <a href="/principal/reports" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg <%= currentPage === 'reports' ? 'active' : '' %>">
                        <i class="fas fa-chart-pie mr-3 text-purple-600"></i>
                        Executive Reports
                    </a>

                    <div class="border-t border-gray-200 my-4"></div>

                    <a href="/principal/profile" class="sidebar-link flex items-center px-4 py-3 text-gray-700 rounded-lg">
                        <i class="fas fa-user mr-3"></i>
                        Profile
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Executive Page Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-principal-dark"><%= title %></h1>
                        <p class="text-principal-silver mt-1 flex items-center">
                            <i class="fas fa-shield-alt text-principal-accent mr-2"></i>
                            Executive leadership and strategic oversight
                        </p>
                    </div>
                    <div class="leadership-badge px-4 py-2 rounded-full">
                        <i class="fas fa-crown mr-2"></i>
                        <span class="font-bold">PRINCIPAL</span>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <% if (typeof success !== 'undefined' && success) { %>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <%= success %>
                </div>
            <% } %>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <%= error %>
                </div>
            <% } %>

            <!-- Page Content -->
            <%- body %>
        </main>
    </div>

    <!-- Page-specific JavaScript -->
    <% if (currentPage === 'infrastructure') { %>
        <script src="/js/infrastructure.js"></script>
    <% } %>
    <% if (currentPage === 'students') { %>
        <script src="/js/students-page.js"></script>
    <% } %>
    <% if (currentPage === 'teacher-management') { %>
        <script src="/js/teacher-management-combined.js?v=<%= Date.now() %>"></script>
    <% } %>

    <!-- JavaScript -->
    <script>
        // Real-time clock
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
            document.getElementById('current-time').innerHTML = `${dateString}<br>${timeString}`;
        }

        updateTime();
        setInterval(updateTime, 1000);

        // Auto-refresh functionality for real-time updates
        let autoRefreshInterval;

        function startAutoRefresh(intervalMs = 30000) {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }

            autoRefreshInterval = setInterval(() => {
                // Only refresh if the page is visible
                if (!document.hidden) {
                    refreshPageData();
                }
            }, intervalMs);
        }

        function refreshPageData() {
            // This will be overridden by individual pages
            console.log('Refreshing page data...');
        }

        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            } else {
                startAutoRefresh();
            }
        });

        // Utility functions
        function formatNumber(num) {
            return new Intl.NumberFormat().format(num);
        }

        function formatPercentage(num) {
            return `${parseFloat(num).toFixed(1)}%`;
        }

        function getStatusColor(status) {
            const colors = {
                'excellent': 'text-green-600',
                'good': 'text-blue-600',
                'average': 'text-yellow-600',
                'poor': 'text-red-600',
                'delivered': 'text-green-600',
                'completed': 'text-green-600',
                'pending': 'text-yellow-600',
                'cancelled': 'text-red-600',
                'overdue': 'text-red-600'
            };
            return colors[status] || 'text-gray-600';
        }

        // Initialize tooltips and other interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add any initialization code here
            console.log('Principal dashboard loaded');
        });
    </script>
</body>
</html>
