<!-- Faculty Details Page -->
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-lg">
        <!-- Header -->
        <div class="bg-gray-100 p-4 rounded-t-lg border-b">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">Faculty Details</h2>
                <div class="flex space-x-2">
                    <input type="text" id="search-teachers" placeholder="Search teachers..." 
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                    <select id="filter-completion" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">All Profiles</option>
                        <option value="complete">Complete (≥80%)</option>
                        <option value="incomplete">Incomplete (<80%)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Experience</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile Completion</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                    <% if (teachers && teachers.length > 0) { %>
                        <% teachers.forEach(teacher => { %>
                            <%
                                // Simplified profile completion calculation
                                let completedFields = 0;
                                let totalFields = 23;

                                // Count completed basic fields
                                if (teacher.name || teacher.full_name) completedFields++;
                                if (teacher.email) completedFields++;
                                if (teacher.username) completedFields++;
                                if (teacher.date_of_birth) completedFields++;
                                if (teacher.gender) completedFields++;
                                if (teacher.phone) completedFields++;
                                if (teacher.employee_id) completedFields++;
                                if (teacher.bio) completedFields++;

                                // Count completed professional fields
                                if (teacher.designation) completedFields++;
                                if (teacher.department) completedFields++;
                                if (teacher.joining_date) completedFields++;
                                if (teacher.employment_type) completedFields++;
                                if (teacher.total_experience_years) completedFields++;
                                if (teacher.emergency_contact) completedFields++;
                                if (teacher.address) completedFields++;
                                if (teacher.office_location) completedFields++;
                                if (teacher.languages_known) completedFields++;
                                if (teacher.subjects || teacher.subjects_taught) completedFields++;

                                // Estimate enhanced fields
                                if (completedFields > 10) {
                                    completedFields += 2;
                                } else if (completedFields > 5) {
                                    completedFields += 1;
                                }

                                // Calculate completion percentage
                                let profileCompletion = Math.round((completedFields / totalFields) * 100);
                                if (profileCompletion > 100) profileCompletion = 100;

                                // Determine colors
                                const progressBarColor = profileCompletion >= 80 ? '#3b82f6' : '#6b7280';
                                const textColor = profileCompletion >= 80 ? 'text-blue-600' : 'text-gray-600';
                                const profileLevel = profileCompletion >= 80 ? 'complete' : 'incomplete';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-teacher-id="<%= teacher.id %>"
                                data-name="<%= (teacher.name || '').toLowerCase() %>"
                                data-email="<%= (teacher.email || '').toLowerCase() %>"
                                data-profile-completion="<%= profileLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium">
                                                    <%= (teacher.name || 'U').split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name || 'Unknown Teacher' %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email || 'No email' %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.department || 'Academic' %></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <%= teacher.designation || 'Teacher' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.total_experience_years || '0' %></span> years
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total experience
                                    </div>
                                    <% if (teacher.teaching_experience_years && teacher.teaching_experience_years !== teacher.total_experience_years) { %>
                                        <div class="text-xs text-gray-400">
                                            <%= teacher.teaching_experience_years %> years teaching
                                        </div>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full" style="width: <%= profileCompletion %>%; background-color: <%= progressBarColor %>;"></div>
                                        </div>
                                        <span class="text-sm font-medium <%= textColor %>">
                                            <%= profileCompletion %>%
                                        </span>
                                    </div>
                                    <div class="text-xs mt-1 <%= textColor %>">
                                        <%= completedFields %>/<%= totalFields %> fields
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="view-teacher-btn hover:bg-gray-100 p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn hover:bg-gray-100 p-1 rounded"
                                                data-action="send-message"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button class="generate-cv-btn hover:bg-gray-100 p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Generate CV PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    <% } else { %>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Faculty Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 overflow-hidden">
  <div class="h-full flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-2xl w-full max-w-7xl h-[65vh] flex flex-col">
      <!-- Modal Header - Compact -->
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-t-lg flex-shrink-0">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <div id="modal-header-avatar" class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-xs font-bold">
              <!-- Initials will be populated -->
            </div>
            <div>
              <h3 id="teacherModalTitle" class="text-base font-semibold">Faculty Profile</h3>
              <p id="modal-header-subtitle" class="text-xs opacity-90">Loading...</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button id="printTeacherProfile" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-xs font-medium transition-colors">
              <i class="fas fa-file-pdf mr-1"></i>Download CV
            </button>
            <button id="closeTeacherModalBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-1 rounded transition-colors">
              <i class="fas fa-times text-sm"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Modal Content - Full Width Tabbed Layout -->
      <div class="flex-1 overflow-hidden flex flex-col min-h-0">
        <!-- Teacher General Information - Above Tabs -->
        <div class="bg-gray-50 border-b border-gray-200 px-4 py-3 flex-shrink-0">
          <div class="flex items-center justify-between">
            <!-- Teacher Basic Info -->
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-blue-600 text-lg"></i>
              </div>
              <div>
                <h3 id="modal-teacher-name-header" class="text-lg font-semibold text-gray-900">Loading...</h3>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                  <span><i class="fas fa-id-badge mr-1"></i><span id="modal-teacher-id-header">ID: Loading...</span></span>
                  <span><i class="fas fa-briefcase mr-1"></i><span id="modal-teacher-designation-header">Loading...</span></span>
                  <span><i class="fas fa-building mr-1"></i><span id="modal-teacher-department-header">Loading...</span></span>
                </div>
              </div>
            </div>
            <!-- Quick Stats -->
            <div class="flex items-center space-x-6 text-sm">
              <div class="text-center">
                <div class="text-lg font-semibold text-blue-600" id="modal-experience-years-header">0</div>
                <div class="text-xs text-gray-500">Years Exp.</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-green-600" id="modal-profile-completion-header">0%</div>
                <div class="text-xs text-gray-500">Profile</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-purple-600" id="modal-performance-rating-header">-</div>
                <div class="text-xs text-gray-500">Rating</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 px-4 flex-shrink-0">
          <nav class="flex space-x-6" aria-label="Tabs">
            <button class="tab-btn active border-b-2 border-blue-500 py-2 px-1 text-xs font-medium text-blue-600" data-tab="overview">
              <i class="fas fa-user mr-1"></i> Personal Information
            </button>
            <button class="tab-btn border-b-2 border-transparent py-2 px-1 text-xs font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="skills">
              <i class="fas fa-cogs mr-1"></i> Skills
            </button>
            <button class="tab-btn border-b-2 border-transparent py-2 px-1 text-xs font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="achievements">
              <i class="fas fa-trophy mr-1"></i> Achievements
            </button>
            <button class="tab-btn border-b-2 border-transparent py-2 px-1 text-xs font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="research">
              <i class="fas fa-flask mr-1"></i> Research
            </button>
          </nav>
        </div>

        <!-- Tab Content Area -->
        <div class="flex-1 overflow-hidden">
          <div id="teacherModalContent" class="h-full">
            <!-- Loading state -->
            <div id="modal-loading" class="flex items-center justify-center h-full">
              <div class="text-center">
                <i class="fas fa-spinner fa-spin text-3xl text-blue-600 mb-4"></i>
                <p class="text-gray-600">Loading faculty profile...</p>
              </div>
            </div>
            <!-- Tab Content -->
            <div id="enhanced-profile-content" class="hidden h-full">
              <div class="tab-content h-full">
                <!-- Overview Tab -->
                <div id="tab-overview" class="tab-panel active h-full overflow-y-auto p-4">
                  <!-- Contact Information -->
                  <div class="mb-6">
                    <h5 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                      <i class="fas fa-phone text-green-500 mr-2"></i> Contact Information
                    </h5>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div class="bg-white border rounded p-4">
                        <label class="text-sm text-gray-600 block mb-2">Email</label>
                        <p id="modal-teacher-email" class="text-sm text-gray-900">Loading...</p>
                      </div>
                      <div class="bg-white border rounded p-4">
                        <label class="text-sm text-gray-600 block mb-2">Phone</label>
                        <p id="modal-teacher-phone" class="text-sm text-gray-900">Loading...</p>
                      </div>
                      <div class="bg-white border rounded p-4">
                        <label class="text-sm text-gray-600 block mb-2">Emergency Contact</label>
                        <p id="modal-emergency-contact" class="text-sm text-gray-900">Loading...</p>
                      </div>
                    </div>
                  </div>

                  <!-- Address Information -->
                  <div class="mb-6">
                    <h5 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                      <i class="fas fa-map-marker-alt text-red-500 mr-2"></i> Address Information
                    </h5>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="bg-white border rounded p-4">
                        <label class="text-sm text-gray-600 block mb-2">Home Address</label>
                        <p id="modal-address" class="text-sm text-gray-900">Loading...</p>
                        <div class="text-sm text-gray-600 mt-2">
                          <span id="modal-city">City</span>, <span id="modal-state">State</span> <span id="modal-pincode">Pin</span>
                        </div>
                      </div>
                      <div class="bg-white border rounded p-4">
                        <label class="text-sm text-gray-600 block mb-2">Office Location</label>
                        <p id="modal-office-location" class="text-sm text-gray-900">Loading...</p>
                      </div>
                    </div>
                  </div>

                </div>

                <!-- Education and Experience tabs removed as requested -->

                <!-- Skills & Languages Tab -->
                <div id="tab-skills" class="tab-panel h-full overflow-y-auto p-4">
                  <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-cogs text-purple-500 mr-1"></i> Skills
                      </h5>
                      <div id="modal-skills-list" class="space-y-2">
                        <!-- Skills will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-cogs text-lg mb-1"></i>
                          <p class="text-xs">Skills information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-language text-indigo-500 mr-1"></i> Languages
                      </h5>
                      <div id="modal-languages-list" class="space-y-2">
                        <!-- Languages will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-language text-lg mb-1"></i>
                          <p class="text-xs">Languages information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-certificate text-yellow-500 mr-1"></i> Certifications
                      </h5>
                      <div id="modal-certifications-list" class="space-y-2">
                        <!-- Certifications will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-certificate text-lg mb-1"></i>
                          <p class="text-xs">Certifications information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-award text-orange-500 mr-1"></i> Other Qualifications
                      </h5>
                      <div id="modal-other-qualifications-list" class="space-y-2">
                        <!-- Other qualifications will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-award text-lg mb-1"></i>
                          <p class="text-xs">Qualifications information</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Achievements Tab -->
                <div id="tab-achievements" class="tab-panel h-full overflow-y-auto p-4">
                  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-trophy text-yellow-500 mr-1"></i> Awards
                      </h5>
                      <div id="modal-awards-list" class="space-y-2">
                        <!-- Awards will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-trophy text-lg mb-1"></i>
                          <p class="text-xs">Awards information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-chalkboard-teacher text-teal-500 mr-1"></i> Training Programs
                      </h5>
                      <div id="modal-training-list" class="space-y-2">
                        <!-- Training programs will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-chalkboard-teacher text-lg mb-1"></i>
                          <p class="text-xs">Training programs information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-star text-purple-500 mr-1"></i> Enhanced Achievements
                      </h5>
                      <div id="modal-achievements-content" class="space-y-2">
                        <!-- Enhanced achievements will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-star text-lg mb-1"></i>
                          <p class="text-xs">Enhanced achievements information</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Research Tab -->
                <div id="tab-research" class="tab-panel h-full overflow-y-auto p-4">
                  <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-book text-blue-500 mr-1"></i> Publications
                      </h5>
                      <div id="modal-publications-list" class="space-y-2">
                        <!-- Publications will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-book text-lg mb-1"></i>
                          <p class="text-xs">Publications information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-file-alt text-green-500 mr-1"></i> Research Papers
                      </h5>
                      <div id="modal-research-papers-list" class="space-y-2">
                        <!-- Research papers will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-file-alt text-lg mb-1"></i>
                          <p class="text-xs">Research papers information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-users text-purple-500 mr-1"></i> Conferences
                      </h5>
                      <div id="modal-conferences-list" class="space-y-2">
                        <!-- Conferences will be populated by JavaScript -->
                        <div class="bg-gray-50 p-2 rounded border text-center text-gray-500">
                          <i class="fas fa-users text-lg mb-1"></i>
                          <p class="text-xs">Conferences information</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 class="text-xs font-semibold text-gray-900 mb-2 flex items-center">
                        <i class="fas fa-sticky-note text-gray-500 mr-1"></i> Additional Notes
                      </h5>
                      <div id="modal-notes" class="bg-gray-50 p-2 rounded text-sm text-gray-900">
                        <!-- Notes will be populated by JavaScript -->
                        <div class="text-center text-gray-500">
                          <i class="fas fa-sticky-note text-lg mb-1"></i>
                          <p class="text-xs">Additional notes information</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


    </div>
  </div>
</div>




<!-- Tab Functionality CSS - COMPREHENSIVE FIX -->
<style>
/* Base tab panel styles */
.tab-panel {
    display: none;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease;
}

/* Active tab panel styles - multiple selectors for maximum specificity */
.tab-panel.active,
.tab-panel.active[style],
.tab-panel[style*="display: block"],
.tab-panel[style="display: block !important"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Tab button styles */
.tab-btn.active {
    border-bottom-color: #3b82f6 !important;
    color: #3b82f6 !important;
}

/* Content area sizing */
.tab-content {
    height: calc(65vh - 100px);
}

.tab-panel {
    max-height: 100%;
    overflow-y: auto;
}

/* Force override any external styles */
#teacherModal .tab-panel {
    display: none;
}

#teacherModal .tab-panel.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure modal content is visible */
#enhanced-profile-content {
    display: block !important;
}

#enhanced-profile-content.hidden {
    display: none !important;
}
</style>

<!-- Teacher Management JavaScript -->
<script src="/js/teacher-management-combined.js"></script>

<!-- Simple JavaScript for table functionality -->
<script>
$(document).ready(function() {
    console.log('Teacher management page loaded');

    // Search functionality
    $('#search-teachers').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.teacher-row').each(function() {
            const name = $(this).data('name') || '';
            const email = $(this).data('email') || '';
            const visible = name.includes(searchTerm) || email.includes(searchTerm);
            $(this).toggle(visible);
        });
    });

    // Filter functionality
    $('#filter-completion').on('change', function() {
        const filterValue = $(this).val();
        $('.teacher-row').each(function() {
            const completion = $(this).data('profile-completion');
            const visible = !filterValue || completion === filterValue;
            $(this).toggle(visible);
        });
    });

    // View teacher button - connect to enhanced modal
    $(document).on('click', '.view-teacher-btn', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        // Call the enhanced modal function
        if (window.openEnhancedTeacherModal) {
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Modal functionality not loaded. Please refresh the page.');
        }
    });

    // Function to populate teacher general information header
    window.populateTeacherHeader = function(teacherData) {
        console.log('🔄 Populating teacher header with data:', teacherData);

        try {
            // Basic teacher info
            $('#modal-teacher-name-header').text(teacherData.name || 'Unknown Teacher');
            $('#modal-teacher-id-header').text('ID: ' + (teacherData.employee_id || teacherData.id || 'N/A'));
            $('#modal-teacher-designation-header').text(teacherData.designation || 'Teacher');
            $('#modal-teacher-department-header').text(teacherData.department || 'General');

            // Quick stats
            $('#modal-experience-years-header').text(teacherData.experience_years || '0');
            $('#modal-profile-completion-header').text((teacherData.profile_completion || 0) + '%');
            $('#modal-performance-rating-header').text(teacherData.performance_rating || '-');

            console.log('✅ Teacher header populated successfully');
        } catch (error) {
            console.error('❌ Error populating teacher header:', error);
        }
    };

    // Enhanced approach to populate header data
    window.populateTeacherHeaderFromRow = function(teacherId) {
        console.log('🔄 Populating header from table row data for teacher:', teacherId);

        // Find the teacher row in the table
        const teacherRow = $(`.teacher-row[data-teacher-id="${teacherId}"]`);
        if (teacherRow.length > 0) {
            const teacherData = {
                name: teacherRow.find('.teacher-name').text().trim(),
                employee_id: teacherRow.find('.teacher-id').text().trim(),
                designation: teacherRow.find('.teacher-designation').text().trim(),
                department: teacherRow.find('.teacher-department').text().trim(),
                experience_years: teacherRow.find('.teacher-experience').text().trim() || '0',
                profile_completion: teacherRow.data('profile-completion') || '0',
                performance_rating: teacherRow.data('performance-rating') || '-'
            };

            console.log('📋 Teacher data from row:', teacherData);
            window.populateTeacherHeader(teacherData);
        } else {
            console.log('❌ Teacher row not found for ID:', teacherId);
            // Use fallback data
            window.populateTeacherHeader({
                name: 'Teacher Name',
                employee_id: teacherId,
                designation: 'Teacher',
                department: 'General',
                experience_years: '5',
                profile_completion: '75',
                performance_rating: '4.2'
            });
        }
    };

    // Override the enhanced modal function to include header population
    const originalOpenModal = window.openEnhancedTeacherModal;
    window.openEnhancedTeacherModal = function(teacherId) {
        console.log('🔄 Enhanced modal opening for teacher:', teacherId);

        // Populate header immediately from table data
        window.populateTeacherHeaderFromRow(teacherId);

        // Call original function
        if (originalOpenModal) {
            originalOpenModal(teacherId);
        } else {
            console.log('⚠️ Original modal function not found, opening modal manually');
            $('#teacherModal').removeClass('hidden');
            $('#enhanced-profile-content').removeClass('hidden');
            $('#modal-loading').addClass('hidden');
        }

        // Also try to populate from currentTeacherData when it becomes available
        setTimeout(() => {
            if (window.currentTeacherData) {
                console.log('🔄 Updating header with loaded data');
                window.populateTeacherHeader(window.currentTeacherData);
            }
        }, 1000);
    };

    // Close modal buttons
    $(document).on('click', '#closeTeacherModalBtn, #closeTeacherModalBtn2', function() {
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // PDF generation button
    $(document).on('click', '#printTeacherProfile', function() {
        console.log('PDF generation requested');
        if (window.currentTeacherData) {
            // Call PDF generation function if available
            if (window.generateTeacherPDF) {
                window.generateTeacherPDF(window.currentTeacherData);
            } else {
                console.log('PDF generation function not available');
                alert('PDF generation functionality will be implemented soon.');
            }
        } else {
            console.error('No teacher data available for PDF generation');
            alert('No teacher data available. Please try again.');
        }
    });

    // Generate CV button in table
    $(document).on('click', '.generate-cv-btn', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('Generate CV for teacher ID:', teacherId);
        alert(`CV generation for teacher ID: ${teacherId} - To be implemented`);
    });

    // Send message button in table
    $(document).on('click', '.action-btn[data-action="send-message"]', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('Send message to teacher ID:', teacherId);
        alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
    });

    // DIRECT TAB FUNCTIONALITY - BYPASS ALL CONFLICTS
    function switchToTab(tabName) {
        console.log('🔄 DIRECT TAB SWITCH - Switching to:', tabName);

        // Hide all panels using direct DOM manipulation
        const allPanels = document.querySelectorAll('.tab-panel');
        allPanels.forEach(panel => {
            panel.style.display = 'none';
            panel.style.visibility = 'hidden';
            panel.style.opacity = '0';
            panel.classList.remove('active');
            console.log('Hidden panel:', panel.id);
        });

        // Show target panel using direct DOM manipulation
        const targetPanel = document.getElementById('tab-' + tabName);
        if (targetPanel) {
            targetPanel.style.display = 'block';
            targetPanel.style.visibility = 'visible';
            targetPanel.style.opacity = '1';
            targetPanel.classList.add('active');
            console.log('✅ Showed panel:', targetPanel.id);
        } else {
            console.error('❌ Panel not found:', 'tab-' + tabName);
        }

        // Update button states using direct DOM manipulation
        const allButtons = document.querySelectorAll('.tab-btn');
        allButtons.forEach(btn => {
            btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
            btn.classList.add('border-transparent', 'text-gray-500');
        });

        const targetButton = document.querySelector('.tab-btn[data-tab="' + tabName + '"]');
        if (targetButton) {
            targetButton.classList.add('active', 'border-blue-500', 'text-blue-600');
            targetButton.classList.remove('border-transparent', 'text-gray-500');
            console.log('✅ Updated button for:', tabName);
        }

        console.log('✅ DIRECT TAB SWITCH COMPLETE');
    }

    // Make function globally available
    window.switchToTab = switchToTab;

    // Enhanced event listener with detailed debugging
    document.addEventListener('click', function(e) {
        console.log('🔍 Click detected on:', e.target);

        // Check if clicked element is a tab button or inside one
        let tabButton = null;
        if (e.target.classList.contains('tab-btn')) {
            tabButton = e.target;
            console.log('🎯 Direct tab button clicked');
        } else if (e.target.closest('.tab-btn')) {
            tabButton = e.target.closest('.tab-btn');
            console.log('🎯 Child of tab button clicked');
        }

        if (tabButton) {
            e.preventDefault();
            e.stopPropagation();

            const tabName = tabButton.getAttribute('data-tab');
            console.log('🔄 DIRECT CLICK HANDLER - Tab clicked:', tabName);
            console.log('🔍 Tab button element:', tabButton);
            console.log('🔍 Available tab panels:', document.querySelectorAll('.tab-panel').length);

            if (tabName) {
                switchToTab(tabName);
            } else {
                console.error('❌ No data-tab attribute found!');
                console.log('🔍 Button attributes:', tabButton.attributes);
            }
        } else {
            // Only log if it's potentially a tab-related click
            if (e.target.closest('.tab-btn') || e.target.classList.contains('tab-btn')) {
                console.log('🔍 Tab-related click but no button found');
            }
        }
    });

    // Additional jQuery-based tab handler as backup
    $(document).on('click', '.tab-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const tabName = $(this).attr('data-tab');
        console.log('🔄 JQUERY BACKUP HANDLER - Tab clicked:', tabName);

        if (tabName && window.switchToTab) {
            window.switchToTab(tabName);
        }
    });

    // Manual test functions
    window.testTabSwitch = function() {
        console.log('🧪 Testing tab switch functionality');
        console.log('Available tabs:', document.querySelectorAll('.tab-btn').length);
        console.log('Available panels:', document.querySelectorAll('.tab-panel').length);

        // Test switching to skills tab
        if (window.switchToTab) {
            window.switchToTab('skills');
        } else {
            console.error('switchToTab function not available');
        }
    };

    window.debugTabState = function() {
        console.log('🔍 Current tab state:');
        document.querySelectorAll('.tab-btn').forEach((btn, i) => {
            console.log(`Button ${i}:`, {
                element: btn,
                dataTab: btn.getAttribute('data-tab'),
                classes: btn.className,
                visible: btn.offsetParent !== null
            });
        });

        document.querySelectorAll('.tab-panel').forEach((panel, i) => {
            console.log(`Panel ${i}:`, {
                id: panel.id,
                display: panel.style.display,
                visible: panel.offsetParent !== null,
                classes: panel.className
            });
        });
    };

    // Sidebar navigation functionality
    $(document).on('click', '.nav-section-btn', function(e) {
        e.preventDefault();
        const section = $(this).data('section');
        console.log('🔄 Sidebar nav clicked:', section);

        // Activate the corresponding tab
        $(`.tab-btn[data-tab="${section}"]`).click();

        // Highlight the sidebar button
        $('.nav-section-btn').removeClass('bg-blue-50');
        $(this).addClass('bg-blue-50');

        console.log('✅ Sidebar navigation to:', section);
    });

    // Debug function to test tab functionality - IMMEDIATE DEFINITION
    window.debugTabs = function() {
        try {
            console.log('🔍 DEBUG: Tab functionality analysis');
            console.log('📋 Tab buttons found:', $('.tab-btn').length);
            console.log('📋 Tab panels found:', $('.tab-panel').length);

            $('.tab-btn').each(function(i) {
                const $btn = $(this);
                console.log(`Tab ${i}:`, {
                    element: $btn[0],
                    dataTab: $btn.attr('data-tab'),
                    classes: $btn.attr('class'),
                    visible: $btn.is(':visible')
                });
            });

            $('.tab-panel').each(function(i) {
                const $panel = $(this);
                console.log(`Panel ${i}:`, {
                    id: $panel.attr('id'),
                    classes: $panel.attr('class'),
                    display: $panel.css('display'),
                    visible: $panel.is(':visible')
                });
            });
        } catch (error) {
            console.error('❌ Debug function error:', error);
        }
    };

    // Manual tab switching function for testing
    window.switchTab = function(tabName) {
        try {
            console.log('🔄 Manual tab switch to:', tabName);

            // Hide all panels
            $('.tab-panel').each(function() {
                $(this).removeClass('active').hide().css('display', 'none');
            });

            // Show target panel
            const targetPanel = $(`#tab-${tabName}`);
            if (targetPanel.length > 0) {
                targetPanel.addClass('active').show().css('display', 'block');
                console.log('✅ Switched to tab:', tabName);
            } else {
                console.error('❌ Tab not found:', tabName);
            }

            // Update button states
            $('.tab-btn').removeClass('active border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
            $(`.tab-btn[data-tab="${tabName}"]`).addClass('active border-blue-500 text-blue-600').removeClass('border-transparent text-gray-500');

        } catch (error) {
            console.error('❌ Manual tab switch error:', error);
        }
    };

    // Immediate debug function availability test
    console.log('🔧 Setting up debug functions...');
    console.log('🔧 Debug function available:', typeof window.debugTabs);
    console.log('🔧 Switch function available:', typeof window.switchTab);
    console.log('🔧 jQuery available:', typeof $);
    console.log('🔧 Current tab buttons:', $('.tab-btn').length);
    console.log('🔧 Current tab panels:', $('.tab-panel').length);

    // Removed duplicate fallback handler - using main handler only

    // Debug: Log when document is ready
    console.log('📋 Teacher management page JavaScript loaded');
    console.log('🔍 Tab buttons found:', $('.tab-btn').length);
    console.log('🔍 Tab panels found:', $('.tab-panel').length);
    console.log('🔍 Sidebar nav buttons found:', $('.nav-section-btn').length);

    // Test modal visibility function
    window.testModal = function() {
        const modal = $('#teacherModal');
        const isVisible = modal.is(':visible');
        const hasHiddenClass = modal.hasClass('hidden');

        console.log('🔍 Modal test:', {
            exists: modal.length > 0,
            visible: isVisible,
            hasHiddenClass: hasHiddenClass,
            display: modal.css('display'),
            tabButtons: $('.tab-btn').length,
            tabPanels: $('.tab-panel').length
        });

        if (isVisible && !hasHiddenClass) {
            console.log('✅ Modal is visible, running tab debug...');
            if (typeof window.debugTabs === 'function') {
                window.debugTabs();
            }
        } else {
            console.log('⚠️ Modal is not visible. Open a teacher profile first.');
        }
    };

    // Auto-run debug after a short delay to ensure modal is loaded
    setTimeout(function() {
        console.log('🔍 Running automatic tests...');
        if (typeof window.testModal === 'function') {
            window.testModal();
        }
    }, 2000);
});
</script>

<!-- SEPARATE DEBUG SCRIPT - GUARANTEED TO WORK -->
<script>
// Simple debug functions that will definitely load
window.simpleDebug = function() {
    console.log('=== SIMPLE DEBUG ===');
    console.log('jQuery loaded:', typeof $ !== 'undefined');
    console.log('Modal exists:', $('#teacherModal').length > 0);
    console.log('Modal visible:', $('#teacherModal').is(':visible'));
    console.log('Tab buttons:', $('.tab-btn').length);
    console.log('Tab panels:', $('.tab-panel').length);

    $('.tab-btn').each(function(i) {
        console.log('Button ' + i + ':', $(this).attr('data-tab'), $(this).is(':visible'));
    });

    $('.tab-panel').each(function(i) {
        console.log('Panel ' + i + ':', $(this).attr('id'), $(this).css('display'));
    });
};

window.simpleSwitch = function(tabName) {
    console.log('=== SIMPLE SWITCH TO:', tabName, '===');

    // Hide all panels
    $('.tab-panel').hide();

    // Show target panel
    $('#tab-' + tabName).show();

    // Update buttons
    $('.tab-btn').removeClass('active border-blue-500 text-blue-600').addClass('border-transparent text-gray-500');
    $('.tab-btn[data-tab="' + tabName + '"]').addClass('active border-blue-500 text-blue-600').removeClass('border-transparent text-gray-500');

    console.log('Switch complete. Target panel visible:', $('#tab-' + tabName).is(':visible'));
};

// Removed duplicate simple click handler - using main handler only

console.log('=== SIMPLE DEBUG SCRIPT LOADED ===');
console.log('Available functions: window.simpleDebug(), window.simpleSwitch(tabName)');
</script>

<!-- EMERGENCY DEBUG SCRIPT - IMMEDIATE EXECUTION -->
<script>
console.log('🚨 EMERGENCY SCRIPT LOADING...');
console.log('🚨 jQuery available:', typeof $ !== 'undefined');
console.log('🚨 Document ready state:', document.readyState);

// Immediate function definitions
window.emergencyDebug = function() {
    console.log('🚨 EMERGENCY DEBUG WORKING!');
    console.log('Modal exists:', document.getElementById('teacherModal') ? 'YES' : 'NO');
    console.log('Tab buttons:', document.querySelectorAll('.tab-btn').length);
    console.log('Tab panels:', document.querySelectorAll('.tab-panel').length);
};

window.emergencySwitch = function(tabName) {
    console.log('🚨 EMERGENCY SWITCH TO:', tabName);

    // Hide all panels
    var panels = document.querySelectorAll('.tab-panel');
    for (var i = 0; i < panels.length; i++) {
        panels[i].style.display = 'none';
        panels[i].classList.remove('active');
    }

    // Show target panel
    var targetPanel = document.getElementById('tab-' + tabName);
    if (targetPanel) {
        targetPanel.style.display = 'block';
        targetPanel.classList.add('active');
        console.log('✅ Switched to:', tabName);
    } else {
        console.log('❌ Panel not found:', 'tab-' + tabName);
    }

    // Update buttons
    var buttons = document.querySelectorAll('.tab-btn');
    for (var i = 0; i < buttons.length; i++) {
        buttons[i].classList.remove('active', 'border-blue-500', 'text-blue-600');
        buttons[i].classList.add('border-transparent', 'text-gray-500');
    }

    var targetButton = document.querySelector('.tab-btn[data-tab="' + tabName + '"]');
    if (targetButton) {
        targetButton.classList.add('active', 'border-blue-500', 'text-blue-600');
        targetButton.classList.remove('border-transparent', 'text-gray-500');
    }
};

// Emergency click handler
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('tab-btn') || e.target.closest('.tab-btn')) {
        e.preventDefault();
        var button = e.target.classList.contains('tab-btn') ? e.target : e.target.closest('.tab-btn');
        var tabName = button.getAttribute('data-tab');
        console.log('🚨 EMERGENCY CLICK HANDLER:', tabName);
        if (tabName && window.emergencySwitch) {
            window.emergencySwitch(tabName);
        }
    }
});

console.log('🚨 EMERGENCY FUNCTIONS READY!');
console.log('🚨 Available: window.emergencyDebug(), window.emergencySwitch(tabName)');
</script>

<!-- ULTRA SIMPLE TEST SCRIPT -->
<script>
console.log('🔥 ULTRA SIMPLE SCRIPT LOADING...');
window.ultraTest = function() {
    console.log('🔥 ULTRA TEST WORKING!');
    return 'SUCCESS';
};
console.log('🔥 ULTRA SIMPLE SCRIPT LOADED!');
console.log('🔥 Test function:', typeof window.ultraTest);
</script>
