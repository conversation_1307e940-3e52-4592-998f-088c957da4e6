<!-- Faculty Details Overview -->
<div class="mb-8">
    <!-- <PERSON> Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-medium text-gray-900 mb-2">Faculty Details</h1>
        <p class="text-sm text-gray-600">Comprehensive faculty profile management and completion tracking</p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-users text-lg text-gray-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">Total Teachers</p>
                    <p class="text-xl font-medium text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-user-check text-lg text-gray-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">Complete Profiles</p>
                    <p class="text-xl font-medium text-gray-900">
                        <%= teachers.filter(t => {
                            let fields = 0, completed = 0;
                            fields += 8; // Basic fields
                            if (t.name) completed++; if (t.email) completed++; if (t.full_name) completed++;
                            if (t.date_of_birth) completed++; if (t.bio) completed++; if (t.profile_image) completed++;
                            if (t.subjects) completed++; if (t.last_login) completed++;
                            return Math.round((completed / fields) * 100) >= 80;
                        }).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-user-edit text-lg text-gray-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-900">Incomplete Profiles</p>
                    <p class="text-xl font-medium text-gray-900">
                        <%= teachers.filter(t => {
                            let fields = 0, completed = 0;
                            fields += 8; // Basic fields
                            if (t.name) completed++; if (t.email) completed++; if (t.full_name) completed++;
                            if (t.date_of_birth) completed++; if (t.bio) completed++; if (t.profile_image) completed++;
                            if (t.subjects) completed++; if (t.last_login) completed++;
                            return Math.round((completed / fields) * 100) < 80;
                        }).length %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Faculty Details Table -->
<div class="bg-white rounded-lg shadow-sm border">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-medium text-gray-900">Faculty Profile Dashboard</h2>
                <p class="text-sm text-gray-600 mt-1">Comprehensive faculty profile management and completion tracking</p>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Export Button -->
                <button id="exportReport" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Test PDF Button -->
                <button id="testSimplePDF" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-file-pdf mr-2"></i>
                    Test PDF
                </button>
                <!-- Refresh Button -->
                <button id="refreshData" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="searchTeachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent text-sm">
            </div>
            <div class="flex gap-2">
                <select id="filterProfileCompletion" class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-transparent text-sm">
                    <option value="">All Profiles</option>
                    <option value="complete">Complete (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="incomplete">Incomplete (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Experience
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Profile Completion
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachersTableBody">
                        <% teachers.forEach(teacher => { %>
                            <%
                                // Safety check for teacher object
                                if (!teacher) {
                                    return;
                                }

                                // Simplified profile completion calculation
                                let completedFields = 0;
                                let totalFields = 23; // Total expected fields

                                // Count completed basic fields
                                if (teacher.name || teacher.full_name) completedFields++;
                                if (teacher.email) completedFields++;
                                if (teacher.username) completedFields++;
                                if (teacher.date_of_birth) completedFields++;
                                if (teacher.gender) completedFields++;
                                if (teacher.phone) completedFields++;
                                if (teacher.employee_id) completedFields++;
                                if (teacher.bio) completedFields++;

                                // Count completed professional fields
                                if (teacher.designation) completedFields++;
                                if (teacher.department) completedFields++;
                                if (teacher.joining_date) completedFields++;
                                if (teacher.employment_type) completedFields++;
                                if (teacher.total_experience_years) completedFields++;
                                if (teacher.emergency_contact) completedFields++;
                                if (teacher.address) completedFields++;
                                if (teacher.office_location) completedFields++;
                                if (teacher.languages_known) completedFields++;
                                if (teacher.subjects || teacher.subjects_taught) completedFields++;

                                // Estimate enhanced fields (5 fields)
                                if (completedFields > 10) {
                                    completedFields += 2; // Estimate enhanced completion
                                } else if (completedFields > 5) {
                                    completedFields += 1; // Minimal enhanced completion
                                }

                                // Calculate completion percentage
                                let profileCompletion = Math.round((completedFields / totalFields) * 100);
                                if (profileCompletion > 100) profileCompletion = 100;

                                // Determine completion level
                                const profileLevel = profileCompletion >= 80 ? 'complete' : profileCompletion >= 60 ? 'good' : profileCompletion >= 40 ? 'incomplete' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50"
                                data-teacher-id="<%= teacher.id %>"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-profile-completion="<%= profileLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-600">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.department || 'Academic' %></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <%= teacher.designation || 'Teacher' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.experience_years || teacher.total_experience_years || '0' %></span> years
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total experience
                                    </div>
                                    <% if (teacher.teaching_experience_years && teacher.teaching_experience_years !== teacher.total_experience_years) { %>
                                        <div class="text-xs text-gray-400">
                                            <%= teacher.teaching_experience_years %> years teaching
                                        </div>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full bg-gray-600" style="width: <%= profileCompletion %>%;"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= profileCompletion %>%
                                        </span>
                                    </div>
                                    <div class="text-xs mt-1 text-gray-500">
                                        <%= completedFields %>/<%= totalFields %> fields
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="viewTeacherBtn hover:bg-gray-100 p-2 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye text-gray-600"></i>
                                        </button>
                                        <button class="sendMessageBtn hover:bg-gray-100 p-2 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Send Message">
                                            <i class="fas fa-envelope text-gray-600"></i>
                                        </button>
                                        <button class="generateCVBtn hover:bg-gray-100 p-2 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                data-teacher-name="<%= teacher.name %>"
                                                data-teacher-email="<%= teacher.email %>"
                                                title="Generate CV PDF">
                                            <i class="fas fa-file-pdf text-gray-600"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                                    <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        <% } %>
    </div>
</div>

<!-- Faculty Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 id="teacherModalTitle" class="text-lg font-medium text-gray-900">Faculty Details</h3>
                        <p class="text-sm text-gray-500 mt-1" id="teacherModalSubtitle">Loading teacher information...</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button id="downloadCVBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-file-pdf mr-2"></i>Download CV
                        </button>
                        <button id="closeTeacherModalBtn" class="text-gray-400 hover:text-gray-600 p-1">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Teacher General Information (Always Visible) -->
            <div id="teacherGeneralInfo" class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                        <span id="teacherInitials" class="text-xl font-medium text-gray-600">--</span>
                    </div>
                    <div class="flex-1">
                        <h4 id="teacherName" class="text-lg font-medium text-gray-900">Teacher Name</h4>
                        <p id="teacherDesignation" class="text-sm text-gray-600">Designation • Department</p>
                        <p id="teacherContact" class="text-sm text-gray-500">Email • Phone</p>
                    </div>
                    <div class="text-right">
                        <div id="profileCompletionBadge" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                            <span id="completionPercentage">0%</span> Complete
                        </div>
                        <p class="text-xs text-gray-500 mt-1" id="experienceInfo">0 years experience</p>
                    </div>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" id="modalTabs">
                    <button class="tab-btn active py-4 px-1 border-b-2 border-gray-600 font-medium text-sm text-gray-900" data-tab="personal">
                        Personal Information
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="education">
                        Education
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="experience">
                        Experience
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="skills">
                        Skills & Certifications
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="achievements">
                        Achievements
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="overflow-y-auto max-h-96">
                <!-- Loading State -->
                <div id="modalLoading" class="flex items-center justify-center py-12">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mr-3"></i>
                    <span class="text-sm text-gray-600">Loading faculty profile...</span>
                </div>

                <!-- Personal Information Tab -->
                <div id="personalTab" class="tab-content p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">Employee ID:</span> <span id="modalEmployeeId" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Date of Birth:</span> <span id="modalDateOfBirth" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Gender:</span> <span id="modalGender" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Username:</span> <span id="modalUsername" class="text-gray-600">-</span></div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Contact Information</h4>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-medium">Emergency Contact:</span> <span id="modalEmergencyContact" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Address:</span> <span id="modalAddress" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Office Location:</span> <span id="modalOfficeLocation" class="text-gray-600">-</span></div>
                                <div><span class="font-medium">Languages:</span> <span id="modalLanguages" class="text-gray-600">-</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Education Tab -->
                <div id="educationTab" class="tab-content hidden p-6">
                    <div id="modalEducationContent">
                        <p class="text-sm text-gray-500">No education data available</p>
                    </div>
                </div>

                <!-- Experience Tab -->
                <div id="experienceTab" class="tab-content hidden p-6">
                    <div id="modalExperienceContent">
                        <p class="text-sm text-gray-500">No experience data available</p>
                    </div>
                </div>

                <!-- Skills & Certifications Tab -->
                <div id="skillsTab" class="tab-content hidden p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Skills</h4>
                            <div id="modalSkillsContent">
                                <p class="text-sm text-gray-500">No skills data available</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Certifications</h4>
                            <div id="modalCertificationsContent">
                                <p class="text-sm text-gray-500">No certifications available</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Achievements Tab -->
                <div id="achievementsTab" class="tab-content hidden p-6">
                    <div id="modalAchievementsContent">
                        <p class="text-sm text-gray-500">No achievements recorded</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Teacher Management -->
<script src="/js/teacher-management-combined.js?v=<%= Date.now() %>"></script>
