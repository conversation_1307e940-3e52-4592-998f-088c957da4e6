-- Create principal timeline table to track all principals since school inception
-- This table maintains the complete leadership history of the school

CREATE TABLE IF NOT EXISTS principal_timeline (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Principal Information
    principal_name VARCHAR(255) NOT NULL,
    employee_id VARCHAR(50),
    user_id INT NULL, -- NULL for historical principals who may not have user accounts
    
    -- Tenure Information
    start_date DATE NOT NULL,
    end_date DATE NULL, -- NULL for current principal
    tenure_duration_months INT, -- Calculated field
    is_current BOOLEAN DEFAULT FALSE,
    
    -- Position Details
    appointment_type ENUM('regular', 'acting', 'temporary', 'officiating') DEFAULT 'regular',
    appointment_order_number VARCHAR(100),
    appointment_authority VARCHAR(255) DEFAULT 'District Education Office, Ludhiana',
    
    -- Background Information
    previous_position VARCHAR(255),
    previous_school VARCHAR(255),
    educational_background TEXT,
    experience_years INT DEFAULT 0,
    
    -- Achievements During Tenure
    major_achievements TEXT,
    infrastructure_developments TEXT,
    academic_improvements TEXT,
    awards_during_tenure TEXT,
    
    -- Contact Information (if available)
    phone VARCHAR(15),
    email VARCHAR(255),
    
    -- Additional Information
    retirement_date DATE NULL,
    transfer_details TEXT,
    reason_for_leaving ENUM('transfer', 'promotion', 'retirement', 'resignation', 'other') NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    INDEX idx_is_current (is_current),
    INDEX idx_user_id (user_id)
);

-- Insert historical principal data since school inception (2014)
INSERT INTO principal_timeline (
    principal_name, employee_id, user_id, start_date, end_date, 
    tenure_duration_months, is_current, appointment_type,
    appointment_order_number, appointment_authority,
    previous_position, previous_school, educational_background, experience_years,
    major_achievements, infrastructure_developments, academic_improvements,
    awards_during_tenure, reason_for_leaving
) VALUES 
-- First Principal (2014-2017)
(
    'Mr. Rajesh Kumar Sharma',
    'PRIN-2014-001',
    NULL,
    '2014-07-01',
    '2017-06-30',
    36,
    FALSE,
    'regular',
    'APT-2014-GSSS-001',
    'District Education Office, Ludhiana',
    'Vice Principal, Government High School, Jalandhar',
    'Government High School, Jalandhar',
    'M.A. (Education), B.Ed, 15 years teaching experience',
    15,
    'Established school infrastructure, Set up initial academic framework, Introduced computer education',
    'Built 12 classrooms, Established computer lab, Set up library with 2000 books',
    'Achieved 85% pass rate in first board examination (2016), Introduced science stream',
    'Best New School Setup Award - District Education Office (2015)',
    'transfer'
),

-- Second Principal (2017-2020)
(
    'Mrs. Sunita Devi Gupta',
    'PRIN-2017-002',
    NULL,
    '2017-07-01',
    '2020-03-31',
    33,
    FALSE,
    'regular',
    'APT-2017-GSSS-002',
    'District Education Office, Ludhiana',
    'Principal, Government Senior Secondary School, Khanna',
    'Government Senior Secondary School, Khanna',
    'M.Ed, B.Ed, M.A. (Mathematics), 18 years experience',
    18,
    'Improved academic performance, Introduced sports programs, Enhanced teacher training',
    'Built science laboratory, Added 6 more classrooms, Developed playground facilities',
    'Increased pass rate to 92%, Introduced commerce stream, Started career counseling',
    'Excellence in Academic Improvement - Punjab Education Department (2019)',
    'retirement'
),

-- Third Principal (2020-2022) - Acting during COVID
(
    'Mr. Harpreet Singh Brar',
    'PRIN-2020-003',
    NULL,
    '2020-04-01',
    '2022-11-11',
    31,
    FALSE,
    'acting',
    'APT-2020-GSSS-003',
    'District Education Office, Ludhiana',
    'Vice Principal, Government Senior Secondary School, Model Town',
    'Government Senior Secondary School, Model Town',
    'M.Ed, B.Ed, M.Sc. (Physics), 12 years experience',
    12,
    'Managed school during COVID-19 pandemic, Implemented online education, Maintained academic continuity',
    'Set up digital infrastructure, Installed internet connectivity, Created online learning platform',
    'Successfully conducted online classes during pandemic, Maintained 88% student engagement',
    'COVID-19 Management Excellence Award - State Education Department (2021)',
    'promotion'
),

-- Current Principal (2022-present)
(
    'Dr. Sarah Johnson',
    'PRIN001',
    105,
    '2022-11-12',
    NULL,
    NULL,
    TRUE,
    'regular',
    'APT-2022-GSSS-004',
    'District Education Office, Ludhiana',
    'Vice Principal, Government Senior Secondary School, Patiala',
    'Government Senior Secondary School, Patiala',
    'Ph.D. (Educational Leadership), M.Ed, B.Ed, 10 years experience',
    10,
    'Digital transformation of school, Enhanced infrastructure, Improved teacher development programs',
    'Upgraded computer lab, Added smart classrooms, Improved library facilities, Enhanced sports infrastructure',
    'Achieved 94% pass rate, Introduced arts stream, Started skill development programs',
    'Best Teacher Award - District Education Office Jalandhar (2018), Excellence in Educational Leadership - Punjab Education Department (2021)',
    NULL
);

-- Update tenure duration for current principal
UPDATE principal_timeline 
SET tenure_duration_months = TIMESTAMPDIFF(MONTH, start_date, CURDATE())
WHERE is_current = TRUE;
