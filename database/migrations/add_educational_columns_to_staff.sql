-- Add educational qualification columns to staff table
-- This migration adds comprehensive educational qualification fields to the staff table

ALTER TABLE staff 
ADD COLUMN class_10_board VARCHAR(100) AFTER pincode,
ADD COLUMN class_10_year YEAR AFTER class_10_board,
ADD COLUMN class_10_percentage DECIMAL(5,2) AFTER class_10_year,
ADD COLUMN class_10_school VARCHAR(255) AFTER class_10_percentage,

ADD COLUMN class_12_board VARCHAR(100) AFTER class_10_school,
ADD COLUMN class_12_year YEAR AFTER class_12_board,
ADD COLUMN class_12_percentage DECIMAL(5,2) AFTER class_12_year,
ADD COLUMN class_12_school VARCHAR(255) AFTER class_12_percentage,
ADD COLUMN class_12_stream VARCHAR(50) AFTER class_12_school,

ADD COLUMN graduation_degree VARCHAR(100) AFTER class_12_stream,
ADD COLUMN graduation_university VARCHAR(255) AFTER graduation_degree,
ADD COLUMN graduation_year YEAR AFTER graduation_university,
ADD COLUMN graduation_percentage DECIMAL(5,2) AFTER graduation_year,
ADD COLUMN graduation_specialization VARCHAR(100) AFTER graduation_percentage,

ADD COLUMN post_graduation_degree VARCHAR(100) AFTER graduation_specialization,
ADD COLUMN post_graduation_university VARCHAR(255) AFTER post_graduation_degree,
ADD COLUMN post_graduation_year YEAR AFTER post_graduation_university,
ADD COLUMN post_graduation_percentage DECIMAL(5,2) AFTER post_graduation_year,
ADD COLUMN post_graduation_specialization VARCHAR(100) AFTER post_graduation_percentage,

ADD COLUMN phd_subject VARCHAR(100) AFTER post_graduation_specialization,
ADD COLUMN phd_university VARCHAR(255) AFTER phd_subject,
ADD COLUMN phd_year YEAR AFTER phd_university,
ADD COLUMN phd_thesis_title VARCHAR(500) AFTER phd_year,

ADD COLUMN other_qualifications TEXT AFTER phd_thesis_title,
ADD COLUMN professional_certifications TEXT AFTER other_qualifications,
ADD COLUMN special_skills TEXT AFTER professional_certifications,
ADD COLUMN languages_known VARCHAR(255) AFTER special_skills;
