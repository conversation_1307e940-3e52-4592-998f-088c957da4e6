-- =====================================================
-- SCHOOL CAPACITY AUTO-UPDATE TRIGGERS
-- =====================================================
-- This file contains MySQL triggers that automatically update
-- the school_information table whenever classrooms are added,
-- modified, or deleted from the rooms table.
--
-- Created: 2025-06-01
-- Purpose: Maintain real-time synchronization between classroom
--          infrastructure and school capacity data
-- =====================================================

-- =====================================================
-- 1. STORED PROCEDURE: UpdateSchoolCapacity
-- =====================================================
-- This procedure calculates the total classroom count and
-- student capacity from all rooms with room_number LIKE 'Room %'
-- and updates the school_information table accordingly.

DELIMITER $$

CREATE PROCEDURE UpdateSchoolCapacity()
BEGIN
    DECLARE total_classrooms INT DEFAULT 0;
    DECLARE total_capacity INT DEFAULT 0;
    
    -- Calculate current totals from rooms table
    SELECT 
        COUNT(r.id),
        COALESCE(SUM(r.capacity), 0)
    INTO total_classrooms, total_capacity
    FROM rooms r
    WHERE r.room_number LIKE 'Room %'
    AND r.capacity > 0;
    
    -- Update school_information table
    UPDATE school_information 
    SET 
        total_classrooms = total_classrooms,
        total_students_capacity = total_capacity,
        updated_at = NOW()
    WHERE is_active = 1;
END$$

DELIMITER ;

-- =====================================================
-- 2. INSERT TRIGGER: rooms_capacity_insert_trigger
-- =====================================================
-- Automatically updates school capacity when a new classroom is added

DELIMITER $$

CREATE TRIGGER rooms_capacity_insert_trigger
AFTER INSERT ON rooms
FOR EACH ROW
BEGIN
    -- Only update if the inserted room is a classroom
    IF NEW.room_number LIKE 'Room %' AND NEW.capacity > 0 THEN
        CALL UpdateSchoolCapacity();
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 3. UPDATE TRIGGER: rooms_capacity_update_trigger
-- =====================================================
-- Automatically updates school capacity when a classroom is modified

DELIMITER $$

CREATE TRIGGER rooms_capacity_update_trigger
AFTER UPDATE ON rooms
FOR EACH ROW
BEGIN
    -- Update if either old or new room is a classroom, or if capacity changed
    IF (OLD.room_number LIKE 'Room %' OR NEW.room_number LIKE 'Room %') 
       OR (OLD.capacity != NEW.capacity) THEN
        CALL UpdateSchoolCapacity();
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 4. DELETE TRIGGER: rooms_capacity_delete_trigger
-- =====================================================
-- Automatically updates school capacity when a classroom is deleted

DELIMITER $$

CREATE TRIGGER rooms_capacity_delete_trigger
AFTER DELETE ON rooms
FOR EACH ROW
BEGIN
    -- Only update if the deleted room was a classroom
    IF OLD.room_number LIKE 'Room %' AND OLD.capacity > 0 THEN
        CALL UpdateSchoolCapacity();
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 5. INITIAL SYNC
-- =====================================================
-- Run this once to sync current data after creating the triggers

CALL UpdateSchoolCapacity();

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================
-- Use these queries to verify the triggers are working correctly

-- Check current school capacity
SELECT total_classrooms, total_students_capacity, updated_at 
FROM school_information 
WHERE is_active = 1;

-- Check current classroom count and capacity from rooms table
SELECT 
    COUNT(r.id) as actual_classrooms,
    SUM(r.capacity) as actual_capacity
FROM rooms r
WHERE r.room_number LIKE 'Room %'
AND r.capacity > 0;

-- =====================================================
-- 7. TEST SCENARIOS
-- =====================================================
-- Use these commands to test the triggers (optional)

-- Test INSERT trigger
-- INSERT INTO rooms (room_number, capacity, floor, building) 
-- VALUES ('Room 99', 50, 2, 'Test Building');

-- Test UPDATE trigger
-- UPDATE rooms SET capacity = 60 WHERE room_number = 'Room 99';

-- Test DELETE trigger
-- DELETE FROM rooms WHERE room_number = 'Room 99';

-- =====================================================
-- 8. TRIGGER MANAGEMENT
-- =====================================================
-- Commands to manage the triggers if needed

-- View all triggers
-- SHOW TRIGGERS LIKE 'rooms%';

-- Drop triggers (if needed)
-- DROP TRIGGER IF EXISTS rooms_capacity_insert_trigger;
-- DROP TRIGGER IF EXISTS rooms_capacity_update_trigger;
-- DROP TRIGGER IF EXISTS rooms_capacity_delete_trigger;

-- Drop procedure (if needed)
-- DROP PROCEDURE IF EXISTS UpdateSchoolCapacity;

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. These triggers only affect rooms with room_number LIKE 'Room %'
-- 2. Only rooms with capacity > 0 are counted
-- 3. The school_information table must have is_active = 1 record
-- 4. Triggers automatically update the updated_at timestamp
-- 5. All operations are atomic and will rollback on error
-- =====================================================
