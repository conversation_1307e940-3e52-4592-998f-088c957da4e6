/**
 * Teacher Management Combined JavaScript
 * Consolidated functionality for teacher management page
 */

console.log('🚀 Teacher Management JavaScript file loaded!');

$(document).ready(function() {
    console.log('✅ Teacher Management System Initialized - DOM Ready!');

    // Global variables
    let currentTeacherData = null;
    let currentTeacherId = null;

    // Initialize page functionality
    initializeEventHandlers();
    initializeSearch();
    initializeFilters();

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // View teacher button
        $(document).on('click', '.viewTeacherBtn', handleViewTeacher);
        
        // Generate CV button
        $(document).on('click', '.generateCVBtn', handleGenerateCV);
        
        // Send message button
        $(document).on('click', '.sendMessageBtn', handleSendMessage);
        
        // Test PDF button
        $(document).on('click', '#testSimplePDF', handleTestPDF);
        
        // Export report button
        $(document).on('click', '#exportReport', handleExportReport);
        
        // Refresh data button
        $(document).on('click', '#refreshData', handleRefreshData);
        
        // Modal close buttons
        $(document).on('click', '#closeTeacherModalBtn', closeModal);
        
        // Download CV from modal
        $(document).on('click', '#downloadCVBtn', handleDownloadCV);
        
        // Tab navigation
        $(document).on('click', '.tab-btn', handleTabSwitch);
        
        // Modal background click to close
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    }

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        $('#searchTeachers').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTable(searchTerm, $('#filterProfileCompletion').val());
        });
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        $('#filterProfileCompletion').on('change', function() {
            const filterValue = $(this).val();
            filterTable($('#searchTeachers').val().toLowerCase(), filterValue);
        });
    }

    /**
     * Filter table based on search and completion level
     */
    function filterTable(searchTerm, completionFilter) {
        $('#teachersTableBody tr').each(function() {
            const $row = $(this);
            const name = $row.data('name') || '';
            const email = $row.data('email') || '';
            const completionLevel = $row.data('profile-completion') || '';
            
            let showRow = true;
            
            // Search filter
            if (searchTerm && !name.includes(searchTerm) && !email.includes(searchTerm)) {
                showRow = false;
            }
            
            // Completion filter
            if (completionFilter && completionLevel !== completionFilter) {
                showRow = false;
            }
            
            $row.toggle(showRow);
        });
    }

    /**
     * Handle view teacher button click
     */
    function handleViewTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        if (!teacherId) {
            console.error('No teacher ID found');
            return;
        }
        
        currentTeacherId = teacherId;
        openModal(teacherId);
    }

    /**
     * Handle generate CV button click
     */
    function handleGenerateCV(e) {
        e.preventDefault();
        const $button = $(this);
        const teacherId = $button.data('teacher-id');
        const teacherName = $button.data('teacher-name');
        const teacherEmail = $button.data('teacher-email');
        
        generateTeacherCV(teacherId, teacherName, teacherEmail, $button);
    }

    /**
     * Handle send message button click
     */
    function handleSendMessage(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        // Placeholder for messaging functionality
        alert(`Send message to teacher ID: ${teacherId}`);
    }

    /**
     * Handle test PDF button click
     */
    function handleTestPDF(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...');
        
        setTimeout(() => {
            generateTestPDF();
            $button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
            
            setTimeout(() => {
                $button.html(originalHtml);
            }, 2000);
        }, 500);
    }

    /**
     * Handle export report button click
     */
    function handleExportReport(e) {
        e.preventDefault();
        
        // Placeholder for export functionality
        alert('Export functionality will be implemented');
    }

    /**
     * Handle refresh data button click
     */
    function handleRefreshData(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');
        
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    /**
     * Handle download CV from modal
     */
    function handleDownloadCV(e) {
        e.preventDefault();
        
        if (currentTeacherData) {
            generateTeacherCV(
                currentTeacherData.id,
                currentTeacherData.name,
                currentTeacherData.email,
                $(this)
            );
        }
    }

    /**
     * Handle tab switching in modal
     */
    function handleTabSwitch(e) {
        e.preventDefault();
        const $button = $(this);
        const tabName = $button.data('tab');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        
        $button.removeClass('border-transparent text-gray-500')
               .addClass('active border-gray-600 text-gray-900');
        
        // Update tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}Tab`).removeClass('hidden');
    }

    /**
     * Open teacher modal
     */
    function openModal(teacherId) {
        $('#teacherModal').removeClass('hidden');
        $('#modalLoading').show();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden');
        
        // Reset tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        $('.tab-btn[data-tab="personal"]').removeClass('border-transparent text-gray-500')
                                          .addClass('active border-gray-600 text-gray-900');
        
        // Load teacher data
        loadTeacherData(teacherId);
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('#teacherModal').addClass('hidden');
        currentTeacherData = null;
        currentTeacherId = null;
    }

    /**
     * Load teacher data for modal
     */
    function loadTeacherData(teacherId) {
        // Show loading state
        $('#modalLoading').show();

        console.log('Loading teacher data for ID:', teacherId);

        // Fetch teacher data from API - using the correct endpoint
        $.ajax({
            url: `/principal/api/teacher/profile-enhanced`,
            method: 'GET',
            data: { teacher_id: teacherId },
            success: function(response) {
                console.log('API Response:', response);
                if (response.success && response.teacher) {
                    currentTeacherData = response.teacher;
                    populateModal(response.teacher);
                } else {
                    console.error('Failed to load teacher data:', response.message);
                    showModalError('Failed to load teacher data: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teacher data:', error);
                console.error('XHR Status:', xhr.status);
                console.error('Response Text:', xhr.responseText);
                showModalError('Error loading teacher data: ' + error);
            },
            complete: function() {
                $('#modalLoading').hide();
            }
        });
    }

    /**
     * Populate modal with teacher data
     */
    function populateModal(data) {
        console.log('Populating modal with data:', data);

        // Update modal title and general info
        const teacherName = data.displayName || data.name || data.full_name || 'Teacher';
        const designation = data.designation || 'Teacher';
        const department = data.department || 'Academic Department';

        $('#teacherModalTitle').text(`${teacherName} - Faculty Details`);
        $('#teacherModalSubtitle').text(`${designation} • ${department}`);

        // Update teacher general info section
        const initials = teacherName.split(' ').map(n => n[0]).join('').toUpperCase();
        $('#teacherInitials').text(initials);
        $('#teacherName').text(teacherName);
        $('#teacherDesignation').text(`${designation} • ${department}`);
        $('#teacherContact').text(`${data.email || 'No email'} • ${data.phone || 'No phone'}`);

        // Update completion info
        const completion = calculateProfileCompletion(data);
        $('#completionPercentage').text(`${completion}%`);
        $('#experienceInfo').text(`${data.total_experience_years || 0} years experience`);

        // Populate personal information tab
        populatePersonalTab(data);

        // Populate other tabs with enhanced data
        populateEducationTab(data);
        populateExperienceTab(data);
        populateSkillsTab(data);
        populateAchievementsTab(data);
    }

    /**
     * Calculate profile completion percentage
     */
    function calculateProfileCompletion(data) {
        let completed = 0;
        const total = 23;
        
        // Count completed fields
        if (data.name) completed++;
        if (data.email) completed++;
        if (data.employee_id) completed++;
        if (data.date_of_birth) completed++;
        if (data.gender) completed++;
        if (data.phone) completed++;
        if (data.designation) completed++;
        if (data.department) completed++;
        if (data.joining_date) completed++;
        if (data.employment_type) completed++;
        if (data.total_experience_years) completed++;
        if (data.emergency_contact) completed++;
        if (data.address) completed++;
        if (data.office_location) completed++;
        if (data.languages_known) completed++;
        if (data.subjects_taught) completed++;
        
        return Math.round((completed / total) * 100);
    }

    /**
     * Populate personal information tab
     */
    function populatePersonalTab(data) {
        $('#modalEmployeeId').text(data.employee_id || '-');
        $('#modalDateOfBirth').text(data.date_of_birth || '-');
        $('#modalGender').text(data.gender || '-');
        $('#modalUsername').text(data.username || '-');
        $('#modalEmergencyContact').text(data.emergency_contact || '-');
        $('#modalAddress').text(data.address || '-');
        $('#modalOfficeLocation').text(data.office_location || '-');
        $('#modalLanguages').text(data.languages_known || '-');
    }

    /**
     * Populate education tab
     */
    function populateEducationTab(data) {
        const $content = $('#modalEducationContent');

        // Check for enhanced education timeline data first
        if (data.educationTimeline && data.educationTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.educationTimeline.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${edu.title || 'Qualification'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <div class="text-xs text-gray-500 mt-1">
                            <span>${edu.year || 'Year'}</span>
                            ${edu.percentage ? ` • ${edu.percentage}%` : ''}
                            ${edu.grade ? ` • Grade: ${edu.grade}` : ''}
                            ${edu.cgpa ? ` • CGPA: ${edu.cgpa}` : ''}
                        </div>
                        ${edu.specialization ? `<p class="text-xs text-gray-600 mt-1">Specialization: ${edu.specialization}</p>` : ''}
                        ${edu.board ? `<p class="text-xs text-gray-600">Board/University: ${edu.board}</p>` : ''}
                        ${edu.thesis ? `<p class="text-xs text-gray-600">Thesis: ${edu.thesis}</p>` : ''}
                        ${edu.achievements ? `<p class="text-xs text-gray-600">Achievements: ${edu.achievements}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else if (data.education && data.education.length > 0) {
            // Fallback to basic education data
            let html = '<div class="space-y-4">';
            data.education.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${edu.degree || 'Degree'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <p class="text-xs text-gray-500">${edu.year || 'Year'} • ${edu.percentage || 'Grade'}</p>
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No education data available</p>');
        }
    }

    /**
     * Populate experience tab
     */
    function populateExperienceTab(data) {
        const $content = $('#modalExperienceContent');

        // Check for enhanced experience timeline data first
        if (data.experienceTimeline && data.experienceTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.experienceTimeline.forEach(exp => {
                const borderColor = exp.isCurrent ? 'border-blue-400' : 'border-gray-300';
                html += `
                    <div class="border-l-4 ${borderColor} pl-4">
                        <div class="flex items-center gap-2">
                            <h5 class="font-medium text-gray-900">${exp.title || 'Position'}</h5>
                            ${exp.isCurrent ? '<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Current</span>' : ''}
                        </div>
                        <p class="text-sm text-gray-600">${exp.institution || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.organizationType ? `<p class="text-xs text-gray-500">Type: ${exp.organizationType}</p>` : ''}
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                        ${exp.responsibilities && exp.responsibilities.length > 0 ? `
                            <div class="mt-2">
                                <p class="text-xs font-medium text-gray-700">Key Responsibilities:</p>
                                <ul class="text-xs text-gray-600 list-disc list-inside ml-2">
                                    ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                        ${exp.achievements && exp.achievements.length > 0 ? `
                            <div class="mt-2">
                                <p class="text-xs font-medium text-gray-700">Achievements:</p>
                                <ul class="text-xs text-gray-600 list-disc list-inside ml-2">
                                    ${exp.achievements.map(ach => `<li>${ach}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                        ${exp.performanceRating ? `<p class="text-xs text-gray-500 mt-1">Performance: ${exp.performanceRating}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else if (data.experience && data.experience.length > 0) {
            // Fallback to basic experience data
            let html = '<div class="space-y-4">';
            data.experience.forEach(exp => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${exp.position || 'Position'}</h5>
                        <p class="text-sm text-gray-600">${exp.organization || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No experience data available</p>');
        }
    }

    /**
     * Populate skills tab
     */
    function populateSkillsTab(data) {
        const $skillsContent = $('#modalSkillsContent');
        const $certificationsContent = $('#modalCertificationsContent');

        // Skills - check for enhanced skillsByCategory data first
        if (data.skillsByCategory && Object.keys(data.skillsByCategory).length > 0) {
            let skillsHtml = '<div class="space-y-4">';
            Object.entries(data.skillsByCategory).forEach(([category, skills]) => {
                skillsHtml += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="flex flex-wrap gap-2">
                `;
                skills.forEach(skill => {
                    const proficiencyColor = skill.proficiency === 'expert' ? 'bg-green-100 text-green-800' :
                                           skill.proficiency === 'advanced' ? 'bg-blue-100 text-blue-800' :
                                           skill.proficiency === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                           'bg-gray-100 text-gray-700';
                    skillsHtml += `
                        <span class="px-2 py-1 ${proficiencyColor} text-xs rounded" title="${skill.proficiency}${skill.experience ? ` - ${skill.experience} years` : ''}">
                            ${skill.name}${skill.certified ? ' ✓' : ''}
                        </span>
                    `;
                });
                skillsHtml += `
                        </div>
                    </div>
                `;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else if (data.skills && data.skills.length > 0) {
            // Fallback to basic skills array
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            data.skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else if (data.special_skills) {
            // Parse special_skills string
            const skills = data.special_skills.split(',').map(s => s.trim());
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else {
            $skillsContent.html('<p class="text-sm text-gray-500">No skills data available</p>');
        }

        // Certifications - check for enhanced certifications data
        if (data.certifications && data.certifications.length > 0) {
            let certHtml = '<div class="space-y-3">';
            data.certifications.forEach(cert => {
                const statusColor = cert.status === 'verified' ? 'text-green-600' :
                                  cert.status === 'pending' ? 'text-yellow-600' : 'text-gray-600';
                certHtml += `
                    <div class="border-l-4 border-gray-200 pl-3">
                        <div class="flex items-center gap-2">
                            <p class="font-medium text-gray-900">${cert.name || 'Certification'}</p>
                            ${cert.status ? `<span class="text-xs ${statusColor}">${cert.status}</span>` : ''}
                        </div>
                        <p class="text-xs text-gray-600">${cert.issuer || 'Issuer'}</p>
                        <div class="text-xs text-gray-500">
                            ${cert.issueDate ? `Issued: ${new Date(cert.issueDate).toLocaleDateString()}` : ''}
                            ${cert.expiryDate && !cert.isLifetime ? ` • Expires: ${new Date(cert.expiryDate).toLocaleDateString()}` : ''}
                            ${cert.isLifetime ? ' • Lifetime' : ''}
                        </div>
                        ${cert.description ? `<p class="text-xs text-gray-600 mt-1">${cert.description}</p>` : ''}
                        ${cert.certificateId ? `<p class="text-xs text-gray-500">ID: ${cert.certificateId}</p>` : ''}
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else if (data.professional_certifications) {
            // Parse professional_certifications string
            const certs = data.professional_certifications.split(',').map(c => c.trim());
            let certHtml = '<div class="space-y-2">';
            certs.forEach(cert => {
                certHtml += `
                    <div class="text-sm">
                        <p class="font-medium text-gray-900">${cert}</p>
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else {
            $certificationsContent.html('<p class="text-sm text-gray-500">No certifications available</p>');
        }
    }

    /**
     * Populate achievements tab
     */
    function populateAchievementsTab(data) {
        const $content = $('#modalAchievementsContent');

        // Check for enhanced achievements by category data first
        if (data.achievementsByCategory && Object.keys(data.achievementsByCategory).length > 0) {
            let html = '<div class="space-y-6">';
            Object.entries(data.achievementsByCategory).forEach(([category, achievements]) => {
                html += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-3 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="space-y-3">
                `;
                achievements.forEach(achievement => {
                    html += `
                        <div class="border-l-4 border-gray-300 pl-4">
                            <h6 class="font-medium text-gray-900">${achievement.title || 'Achievement'}</h6>
                            <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                            <div class="text-xs text-gray-500 mt-1">
                                ${achievement.date ? `Date: ${new Date(achievement.date).toLocaleDateString()}` : ''}
                                ${achievement.issuer ? ` • Issued by: ${achievement.issuer}` : ''}
                                ${achievement.level ? ` • Level: ${achievement.level}` : ''}
                            </div>
                            ${achievement.impact ? `<p class="text-xs text-gray-600 mt-1">Impact: ${achievement.impact}</p>` : ''}
                        </div>
                    `;
                });
                html += `
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else if (data.achievements && data.achievements.length > 0) {
            // Fallback to basic achievements array
            let html = '<div class="space-y-4">';
            data.achievements.forEach(achievement => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${achievement.title || 'Achievement'}</h5>
                        <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                        <p class="text-xs text-gray-500">${achievement.date || 'Date'}</p>
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            // Check for text-based achievement fields
            let hasAchievements = false;
            let html = '<div class="space-y-4">';

            if (data.awards_received) {
                hasAchievements = true;
                const awards = data.awards_received.split(',').map(a => a.trim());
                html += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">Awards & Recognition</h5>
                        <div class="space-y-2">
                `;
                awards.forEach(award => {
                    html += `
                        <div class="border-l-4 border-yellow-300 pl-4">
                            <p class="text-sm text-gray-700">${award}</p>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            if (data.publications) {
                hasAchievements = true;
                const publications = data.publications.split(',').map(p => p.trim());
                html += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">Publications</h5>
                        <div class="space-y-2">
                `;
                publications.forEach(pub => {
                    html += `
                        <div class="border-l-4 border-blue-300 pl-4">
                            <p class="text-sm text-gray-700">${pub}</p>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            if (data.research_papers) {
                hasAchievements = true;
                const papers = data.research_papers.split(',').map(p => p.trim());
                html += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">Research Papers</h5>
                        <div class="space-y-2">
                `;
                papers.forEach(paper => {
                    html += `
                        <div class="border-l-4 border-green-300 pl-4">
                            <p class="text-sm text-gray-700">${paper}</p>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            if (data.conferences_attended) {
                hasAchievements = true;
                const conferences = data.conferences_attended.split(',').map(c => c.trim());
                html += `
                    <div>
                        <h5 class="font-medium text-gray-900 mb-2">Conferences Attended</h5>
                        <div class="space-y-2">
                `;
                conferences.forEach(conf => {
                    html += `
                        <div class="border-l-4 border-purple-300 pl-4">
                            <p class="text-sm text-gray-700">${conf}</p>
                        </div>
                    `;
                });
                html += '</div></div>';
            }

            html += '</div>';

            if (hasAchievements) {
                $content.html(html);
            } else {
                $content.html('<p class="text-sm text-gray-500">No achievements recorded</p>');
            }
        }
    }

    /**
     * Show modal error
     */
    function showModalError(message) {
        $('#modalLoading').hide();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden').html(`
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
                <p class="text-sm text-gray-500">${message}</p>
            </div>
        `);
    }

    /**
     * Generate test PDF
     */
    function generateTestPDF() {
        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test PDF Document</title>
                <style>
                    @page { size: A4; margin: 20mm; }
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
                    h1 { color: #374151; margin-bottom: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .content { margin-bottom: 20px; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class='no-print' style='background: #f3f4f6; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
                    <h3 style='margin: 0 0 10px 0;'>✅ PDF Test Document</h3>
                    <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> or <strong>Cmd+P</strong> to save as PDF</p>
                    <button onclick='window.print()' style='background: #374151; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                    <button onclick='window.close()' style='background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                </div>
                <div class='header'>
                    <h1>✅ PDF Generation Test</h1>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                </div>
                <div class='content'>
                    <p><strong>System:</strong> Teacher Management System</p>
                    <p><strong>Status:</strong> ✅ SUCCESS</p>
                    <p><strong>Test Result:</strong> PDF functionality is working correctly!</p>
                </div>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
    }

    /**
     * Generate teacher CV PDF
     */
    function generateTeacherCV(teacherId, teacherName, teacherEmail, $button) {
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating CV...');

        setTimeout(() => {
            try {
                // Use current teacher data if available, otherwise create basic data
                let teacher = currentTeacherData || {
                    id: teacherId,
                    name: teacherName || 'Teacher Name',
                    email: teacherEmail || '<EMAIL>',
                    designation: 'Teacher',
                    department: 'Academic Department',
                    employee_id: `EMP${String(teacherId).padStart(4, '0')}`,
                    joining_date: new Date().toISOString().split('T')[0],
                    employment_type: 'Permanent',
                    account_status: 'Active'
                };

                const cvHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>${teacher.name || 'Teacher'} - Curriculum Vitae</title>
                        <style>
                            @page { size: A4; margin: 15mm; }
                            body { font-family: Arial, sans-serif; margin: 0; padding: 0; line-height: 1.4; color: #333; }
                            .header { text-align: center; border-bottom: 3px solid #374151; padding-bottom: 15px; margin-bottom: 20px; }
                            .header h1 { color: #374151; margin: 0; font-size: 24px; }
                            .header h2 { color: #6b7280; margin: 5px 0; font-size: 16px; font-weight: normal; }
                            .section { margin-bottom: 20px; }
                            .section-title { background: #374151; color: white; padding: 8px 15px; margin-bottom: 10px; font-weight: bold; }
                            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
                            .info-item { margin-bottom: 8px; }
                            .info-label { font-weight: bold; color: #374151; }
                            .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                            @media print { .no-print { display: none; } }
                        </style>
                    </head>
                    <body>
                        <div class='no-print' style='background: #f3f4f6; padding: 15px; margin-bottom: 20px; border-radius: 5px; border: 1px solid #d1d5db;'>
                            <h3 style='margin: 0 0 10px 0; color: #374151;'>✅ Teacher CV Document</h3>
                            <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
                            <button onclick='window.print()' style='background: #374151; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                            <button onclick='window.close()' style='background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                        </div>
                        <div class='header'>
                            <h1>${teacher.name || 'Teacher Name'}</h1>
                            <h2>${teacher.designation || 'Teacher'}</h2>
                            <p>${teacher.department || 'Academic Department'}</p>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PERSONAL INFORMATION</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Email:</span> ${teacher.email || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Date of Birth:</span> ${teacher.date_of_birth || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Gender:</span> ${teacher.gender || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Employment Type:</span> ${teacher.employment_type || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Account Status:</span> ${teacher.account_status || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PROFESSIONAL EXPERIENCE</div>
                            <div class='info-item'><span class='info-label'>Total Experience:</span> ${teacher.total_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Teaching Experience:</span> ${teacher.teaching_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Subjects Taught:</span> ${teacher.subjects_taught || 'N/A'}</div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>ADMINISTRATIVE DETAILS</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Office Location:</span> ${teacher.office_location || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Performance Rating:</span> ${teacher.performance_rating || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Confirmation Date:</span> ${teacher.confirmation_date || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Last Promotion:</span> ${teacher.last_promotion || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='footer'>
                            <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                            <p>Teacher Management System - Curriculum Vitae</p>
                        </div>
                    </body>
                    </html>
                `;

                const cvWindow = window.open('', '_blank');
                cvWindow.document.write(cvHTML);
                cvWindow.document.close();

                $button.html('<i class="fas fa-check mr-2"></i>CV Generated!');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);

                console.log('✅ CV generated successfully for:', teacher.name);

            } catch(error) {
                console.error('❌ CV generation error:', error);
                $button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);
            }
        }, 500);
    }

    // Expose functions for debugging
    window.teacherManagement = {
        openModal,
        closeModal,
        generateTestPDF,
        generateTeacherCV
    };

    console.log('✅ Teacher Management System Ready');
});
