/**
 * Teacher Management Combined JavaScript
 * Consolidated functionality for teacher management page
 */

$(document).ready(function() {
    console.log('Teacher Management System Initialized');

    // Global variables
    let currentTeacherData = null;
    let currentTeacherId = null;

    // Initialize page functionality
    initializeEventHandlers();
    initializeSearch();
    initializeFilters();

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // View teacher button
        $(document).on('click', '.viewTeacherBtn', handleViewTeacher);
        
        // Generate CV button
        $(document).on('click', '.generateCVBtn', handleGenerateCV);
        
        // Send message button
        $(document).on('click', '.sendMessageBtn', handleSendMessage);
        
        // Test PDF button
        $(document).on('click', '#testSimplePDF', handleTestPDF);
        
        // Export report button
        $(document).on('click', '#exportReport', handleExportReport);
        
        // Refresh data button
        $(document).on('click', '#refreshData', handleRefreshData);
        
        // Modal close buttons
        $(document).on('click', '#closeTeacherModalBtn', closeModal);
        
        // Download CV from modal
        $(document).on('click', '#downloadCVBtn', handleDownloadCV);
        
        // Tab navigation
        $(document).on('click', '.tab-btn', handleTabSwitch);
        
        // Modal background click to close
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    }

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        $('#searchTeachers').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTable(searchTerm, $('#filterProfileCompletion').val());
        });
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        $('#filterProfileCompletion').on('change', function() {
            const filterValue = $(this).val();
            filterTable($('#searchTeachers').val().toLowerCase(), filterValue);
        });
    }

    /**
     * Filter table based on search and completion level
     */
    function filterTable(searchTerm, completionFilter) {
        $('#teachersTableBody tr').each(function() {
            const $row = $(this);
            const name = $row.data('name') || '';
            const email = $row.data('email') || '';
            const completionLevel = $row.data('profile-completion') || '';
            
            let showRow = true;
            
            // Search filter
            if (searchTerm && !name.includes(searchTerm) && !email.includes(searchTerm)) {
                showRow = false;
            }
            
            // Completion filter
            if (completionFilter && completionLevel !== completionFilter) {
                showRow = false;
            }
            
            $row.toggle(showRow);
        });
    }

    /**
     * Handle view teacher button click
     */
    function handleViewTeacher(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        if (!teacherId) {
            console.error('No teacher ID found');
            return;
        }
        
        currentTeacherId = teacherId;
        openModal(teacherId);
    }

    /**
     * Handle generate CV button click
     */
    function handleGenerateCV(e) {
        e.preventDefault();
        const $button = $(this);
        const teacherId = $button.data('teacher-id');
        const teacherName = $button.data('teacher-name');
        const teacherEmail = $button.data('teacher-email');
        
        generateTeacherCV(teacherId, teacherName, teacherEmail, $button);
    }

    /**
     * Handle send message button click
     */
    function handleSendMessage(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        
        // Placeholder for messaging functionality
        alert(`Send message to teacher ID: ${teacherId}`);
    }

    /**
     * Handle test PDF button click
     */
    function handleTestPDF(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...');
        
        setTimeout(() => {
            generateTestPDF();
            $button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
            
            setTimeout(() => {
                $button.html(originalHtml);
            }, 2000);
        }, 500);
    }

    /**
     * Handle export report button click
     */
    function handleExportReport(e) {
        e.preventDefault();
        
        // Placeholder for export functionality
        alert('Export functionality will be implemented');
    }

    /**
     * Handle refresh data button click
     */
    function handleRefreshData(e) {
        e.preventDefault();
        const $button = $(this);
        const originalHtml = $button.html();
        
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');
        
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    /**
     * Handle download CV from modal
     */
    function handleDownloadCV(e) {
        e.preventDefault();
        
        if (currentTeacherData) {
            generateTeacherCV(
                currentTeacherData.id,
                currentTeacherData.name,
                currentTeacherData.email,
                $(this)
            );
        }
    }

    /**
     * Handle tab switching in modal
     */
    function handleTabSwitch(e) {
        e.preventDefault();
        const $button = $(this);
        const tabName = $button.data('tab');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        
        $button.removeClass('border-transparent text-gray-500')
               .addClass('active border-gray-600 text-gray-900');
        
        // Update tab content
        $('.tab-content').addClass('hidden');
        $(`#${tabName}Tab`).removeClass('hidden');
    }

    /**
     * Open teacher modal
     */
    function openModal(teacherId) {
        $('#teacherModal').removeClass('hidden');
        $('#modalLoading').show();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden');
        
        // Reset tab buttons
        $('.tab-btn').removeClass('active border-gray-600 text-gray-900')
                     .addClass('border-transparent text-gray-500');
        $('.tab-btn[data-tab="personal"]').removeClass('border-transparent text-gray-500')
                                          .addClass('active border-gray-600 text-gray-900');
        
        // Load teacher data
        loadTeacherData(teacherId);
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('#teacherModal').addClass('hidden');
        currentTeacherData = null;
        currentTeacherId = null;
    }

    /**
     * Load teacher data for modal
     */
    function loadTeacherData(teacherId) {
        // Show loading state
        $('#modalLoading').show();
        
        // Fetch teacher data from API
        $.ajax({
            url: `/api/teacher-profile-enhanced/${teacherId}`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    currentTeacherData = response.data;
                    populateModal(response.data);
                } else {
                    console.error('Failed to load teacher data:', response.message);
                    showModalError('Failed to load teacher data');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teacher data:', error);
                showModalError('Error loading teacher data');
            },
            complete: function() {
                $('#modalLoading').hide();
            }
        });
    }

    /**
     * Populate modal with teacher data
     */
    function populateModal(data) {
        // Update modal title and general info
        $('#teacherModalTitle').text(`${data.name || 'Teacher'} - Faculty Details`);
        $('#teacherModalSubtitle').text(`${data.designation || 'Teacher'} • ${data.department || 'Academic'}`);
        
        // Update teacher general info section
        const initials = (data.name || 'T').split(' ').map(n => n[0]).join('').toUpperCase();
        $('#teacherInitials').text(initials);
        $('#teacherName').text(data.name || 'Teacher Name');
        $('#teacherDesignation').text(`${data.designation || 'Teacher'} • ${data.department || 'Academic'}`);
        $('#teacherContact').text(`${data.email || 'No email'} • ${data.phone || 'No phone'}`);
        
        // Update completion info
        const completion = calculateProfileCompletion(data);
        $('#completionPercentage').text(`${completion}%`);
        $('#experienceInfo').text(`${data.total_experience_years || 0} years experience`);
        
        // Populate personal information tab
        populatePersonalTab(data);
        
        // Populate other tabs
        populateEducationTab(data);
        populateExperienceTab(data);
        populateSkillsTab(data);
        populateAchievementsTab(data);
    }

    /**
     * Calculate profile completion percentage
     */
    function calculateProfileCompletion(data) {
        let completed = 0;
        const total = 23;
        
        // Count completed fields
        if (data.name) completed++;
        if (data.email) completed++;
        if (data.employee_id) completed++;
        if (data.date_of_birth) completed++;
        if (data.gender) completed++;
        if (data.phone) completed++;
        if (data.designation) completed++;
        if (data.department) completed++;
        if (data.joining_date) completed++;
        if (data.employment_type) completed++;
        if (data.total_experience_years) completed++;
        if (data.emergency_contact) completed++;
        if (data.address) completed++;
        if (data.office_location) completed++;
        if (data.languages_known) completed++;
        if (data.subjects_taught) completed++;
        
        return Math.round((completed / total) * 100);
    }

    /**
     * Populate personal information tab
     */
    function populatePersonalTab(data) {
        $('#modalEmployeeId').text(data.employee_id || '-');
        $('#modalDateOfBirth').text(data.date_of_birth || '-');
        $('#modalGender').text(data.gender || '-');
        $('#modalUsername').text(data.username || '-');
        $('#modalEmergencyContact').text(data.emergency_contact || '-');
        $('#modalAddress').text(data.address || '-');
        $('#modalOfficeLocation').text(data.office_location || '-');
        $('#modalLanguages').text(data.languages_known || '-');
    }

    /**
     * Populate education tab
     */
    function populateEducationTab(data) {
        const $content = $('#modalEducationContent');

        if (data.education && data.education.length > 0) {
            let html = '<div class="space-y-4">';
            data.education.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${edu.degree || 'Degree'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <p class="text-xs text-gray-500">${edu.year || 'Year'} • ${edu.percentage || 'Grade'}</p>
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No education data available</p>');
        }
    }

    /**
     * Populate experience tab
     */
    function populateExperienceTab(data) {
        const $content = $('#modalExperienceContent');

        if (data.experience && data.experience.length > 0) {
            let html = '<div class="space-y-4">';
            data.experience.forEach(exp => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${exp.position || 'Position'}</h5>
                        <p class="text-sm text-gray-600">${exp.organization || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No experience data available</p>');
        }
    }

    /**
     * Populate skills tab
     */
    function populateSkillsTab(data) {
        const $skillsContent = $('#modalSkillsContent');
        const $certificationsContent = $('#modalCertificationsContent');

        // Skills
        if (data.skills && data.skills.length > 0) {
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            data.skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else {
            $skillsContent.html('<p class="text-sm text-gray-500">No skills data available</p>');
        }

        // Certifications
        if (data.certifications && data.certifications.length > 0) {
            let certHtml = '<div class="space-y-2">';
            data.certifications.forEach(cert => {
                certHtml += `
                    <div class="text-sm">
                        <p class="font-medium text-gray-900">${cert.name || 'Certification'}</p>
                        <p class="text-xs text-gray-500">${cert.issuer || 'Issuer'} • ${cert.year || 'Year'}</p>
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else {
            $certificationsContent.html('<p class="text-sm text-gray-500">No certifications available</p>');
        }
    }

    /**
     * Populate achievements tab
     */
    function populateAchievementsTab(data) {
        const $content = $('#modalAchievementsContent');

        if (data.achievements && data.achievements.length > 0) {
            let html = '<div class="space-y-4">';
            data.achievements.forEach(achievement => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-medium text-gray-900">${achievement.title || 'Achievement'}</h5>
                        <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                        <p class="text-xs text-gray-500">${achievement.date || 'Date'}</p>
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No achievements recorded</p>');
        }
    }

    /**
     * Show modal error
     */
    function showModalError(message) {
        $('#modalLoading').hide();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden').html(`
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
                <p class="text-sm text-gray-500">${message}</p>
            </div>
        `);
    }

    /**
     * Generate test PDF
     */
    function generateTestPDF() {
        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Test PDF Document</title>
                <style>
                    @page { size: A4; margin: 20mm; }
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
                    h1 { color: #374151; margin-bottom: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .content { margin-bottom: 20px; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class='no-print' style='background: #f3f4f6; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
                    <h3 style='margin: 0 0 10px 0;'>✅ PDF Test Document</h3>
                    <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> or <strong>Cmd+P</strong> to save as PDF</p>
                    <button onclick='window.print()' style='background: #374151; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                    <button onclick='window.close()' style='background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                </div>
                <div class='header'>
                    <h1>✅ PDF Generation Test</h1>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                </div>
                <div class='content'>
                    <p><strong>System:</strong> Teacher Management System</p>
                    <p><strong>Status:</strong> ✅ SUCCESS</p>
                    <p><strong>Test Result:</strong> PDF functionality is working correctly!</p>
                </div>
            </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
    }

    /**
     * Generate teacher CV PDF
     */
    function generateTeacherCV(teacherId, teacherName, teacherEmail, $button) {
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating CV...');

        setTimeout(() => {
            try {
                // Use current teacher data if available, otherwise create basic data
                let teacher = currentTeacherData || {
                    id: teacherId,
                    name: teacherName || 'Teacher Name',
                    email: teacherEmail || '<EMAIL>',
                    designation: 'Teacher',
                    department: 'Academic Department',
                    employee_id: `EMP${String(teacherId).padStart(4, '0')}`,
                    joining_date: new Date().toISOString().split('T')[0],
                    employment_type: 'Permanent',
                    account_status: 'Active'
                };

                const cvHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>${teacher.name || 'Teacher'} - Curriculum Vitae</title>
                        <style>
                            @page { size: A4; margin: 15mm; }
                            body { font-family: Arial, sans-serif; margin: 0; padding: 0; line-height: 1.4; color: #333; }
                            .header { text-align: center; border-bottom: 3px solid #374151; padding-bottom: 15px; margin-bottom: 20px; }
                            .header h1 { color: #374151; margin: 0; font-size: 24px; }
                            .header h2 { color: #6b7280; margin: 5px 0; font-size: 16px; font-weight: normal; }
                            .section { margin-bottom: 20px; }
                            .section-title { background: #374151; color: white; padding: 8px 15px; margin-bottom: 10px; font-weight: bold; }
                            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
                            .info-item { margin-bottom: 8px; }
                            .info-label { font-weight: bold; color: #374151; }
                            .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                            @media print { .no-print { display: none; } }
                        </style>
                    </head>
                    <body>
                        <div class='no-print' style='background: #f3f4f6; padding: 15px; margin-bottom: 20px; border-radius: 5px; border: 1px solid #d1d5db;'>
                            <h3 style='margin: 0 0 10px 0; color: #374151;'>✅ Teacher CV Document</h3>
                            <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
                            <button onclick='window.print()' style='background: #374151; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                            <button onclick='window.close()' style='background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                        </div>
                        <div class='header'>
                            <h1>${teacher.name || 'Teacher Name'}</h1>
                            <h2>${teacher.designation || 'Teacher'}</h2>
                            <p>${teacher.department || 'Academic Department'}</p>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PERSONAL INFORMATION</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Email:</span> ${teacher.email || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Date of Birth:</span> ${teacher.date_of_birth || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Gender:</span> ${teacher.gender || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Employment Type:</span> ${teacher.employment_type || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Account Status:</span> ${teacher.account_status || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>PROFESSIONAL EXPERIENCE</div>
                            <div class='info-item'><span class='info-label'>Total Experience:</span> ${teacher.total_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Teaching Experience:</span> ${teacher.teaching_experience_years || 0} years</div>
                            <div class='info-item'><span class='info-label'>Subjects Taught:</span> ${teacher.subjects_taught || 'N/A'}</div>
                        </div>
                        <div class='section'>
                            <div class='section-title'>ADMINISTRATIVE DETAILS</div>
                            <div class='info-grid'>
                                <div>
                                    <div class='info-item'><span class='info-label'>Office Location:</span> ${teacher.office_location || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Performance Rating:</span> ${teacher.performance_rating || 'N/A'}</div>
                                </div>
                                <div>
                                    <div class='info-item'><span class='info-label'>Confirmation Date:</span> ${teacher.confirmation_date || 'N/A'}</div>
                                    <div class='info-item'><span class='info-label'>Last Promotion:</span> ${teacher.last_promotion || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div class='footer'>
                            <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                            <p>Teacher Management System - Curriculum Vitae</p>
                        </div>
                    </body>
                    </html>
                `;

                const cvWindow = window.open('', '_blank');
                cvWindow.document.write(cvHTML);
                cvWindow.document.close();

                $button.html('<i class="fas fa-check mr-2"></i>CV Generated!');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);

                console.log('✅ CV generated successfully for:', teacher.name);

            } catch(error) {
                console.error('❌ CV generation error:', error);
                $button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);
            }
        }, 500);
    }

    // Expose functions for debugging
    window.teacherManagement = {
        openModal,
        closeModal,
        generateTestPDF,
        generateTeacherCV
    };

    console.log('✅ Teacher Management System Ready');
});
